using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;

namespace PizzaRestoApp.UI.Reports
{
    /// <summary>
    /// شاشة تقارير العمولات والبرسنتاج
    /// Commission Reports Form
    /// </summary>
    public partial class CommissionReportsForm : Form
    {
        private readonly EmployeeCommissionRepository _commissionRepository;
        private readonly EmployeeRepository _employeeRepository;
        private readonly OrderRepository _orderRepository;

        public CommissionReportsForm()
        {
            InitializeComponent();
            _commissionRepository = new EmployeeCommissionRepository();
            _employeeRepository = new EmployeeRepository();
            _orderRepository = new OrderRepository();

            this.Text = "تقارير العمولات والبرسنتاج - Commission Reports";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            InitializeReports();
        }

        private async void InitializeReports()
        {
            // إعداد التواريخ الافتراضية
            dtpFromDate.Value = DateTime.Now.Date.AddDays(-30);
            dtpToDate.Value = DateTime.Now.Date;

            // تحميل الموظفين
            var employees = await _employeeRepository.GetActiveEmployeesAsync();
            cmbEmployee.DataSource = employees.ToList();
            cmbEmployee.DisplayMember = "FullName";
            cmbEmployee.ValueMember = "Id";
            cmbEmployee.SelectedIndex = -1;

            // إعداد أنواع التقارير
            cmbReportType.Items.Add(new { Text = "تقرير العمولات الشهرية", Value = "Monthly" });
            cmbReportType.Items.Add(new { Text = "تقرير العمولات حسب الموظف", Value = "ByEmployee" });
            cmbReportType.Items.Add(new { Text = "تحليل معدلات العمولة", Value = "RateAnalysis" });
            cmbReportType.Items.Add(new { Text = "مقارنة الأداء", Value = "Performance" });
            cmbReportType.Items.Add(new { Text = "تقرير شامل", Value = "Comprehensive" });
            cmbReportType.DisplayMember = "Text";
            cmbReportType.ValueMember = "Value";
            cmbReportType.SelectedIndex = 0;

            // إعداد Chart
            SetupChart();

            // تحميل التقرير الافتراضي
            LoadMonthlyCommissionReport();
        }

        private void SetupChart()
        {
            chartCommission.Series.Clear();
            chartCommission.ChartAreas.Clear();

            var chartArea = new ChartArea("CommissionArea");
            chartArea.AxisX.Title = "الفترة/الموظف";
            chartArea.AxisY.Title = "العمولة";
            chartArea.AxisX.LabelStyle.Angle = -45;
            chartCommission.ChartAreas.Add(chartArea);

            var series = new Series("العمولات");
            series.ChartType = SeriesChartType.Column;
            series.Color = Color.Green;
            chartCommission.Series.Add(series);
        }

        private async void LoadMonthlyCommissionReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var commissions = await _commissionRepository.GetCommissionsByDateRangeAsync(fromDate, toDate);

                if (cmbEmployee.SelectedValue != null)
                {
                    var employeeId = (int)cmbEmployee.SelectedValue;
                    commissions = commissions.Where(c => c.EmployeeId == employeeId);
                }

                // تجميع العمولات حسب الشهر
                var monthlyData = commissions
                    .GroupBy(c => new { Year = c.CommissionDate.Year, Month = c.CommissionDate.Month })
                    .Select(g => new
                    {
                        Period = $"{g.Key.Year}-{g.Key.Month:00}",
                        TotalCommissions = g.Sum(c => c.CommissionAmount),
                        TotalSales = g.Sum(c => c.SalesAmount),
                        AvgCommissionRate = g.Average(c => c.CommissionRate),
                        TransactionCount = g.Count(),
                        EmployeeCount = g.Select(c => c.EmployeeId).Distinct().Count()
                    })
                    .OrderBy(x => x.Period)
                    .ToList();

                dgvCommissionReport.DataSource = monthlyData;

                // إحصائيات شهرية
                var totalCommissions = monthlyData.Sum(m => m.TotalCommissions);
                var totalSales = monthlyData.Sum(m => m.TotalSales);
                var avgCommissionRate = monthlyData.Count > 0 ? monthlyData.Average(m => m.AvgCommissionRate) : 0;
                var totalTransactions = monthlyData.Sum(m => m.TransactionCount);

                lblTotalCommissions.Text = $"إجمالي العمولات: {totalCommissions:C}";
                lblTotalSales.Text = $"إجمالي المبيعات: {totalSales:C}";
                lblAvgCommissionRate.Text = $"متوسط معدل العمولة: {avgCommissionRate:P}";
                lblTotalTransactions.Text = $"إجمالي المعاملات: {totalTransactions}";

                // تحديث الرسم البياني
                UpdateMonthlyChart(monthlyData);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التقرير الشهري: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateMonthlyChart(List<dynamic> monthlyData)
        {
            chartCommission.Series["العمولات"].Points.Clear();

            foreach (var item in monthlyData)
            {
                chartCommission.Series["العمولات"].Points.AddXY(item.Period, item.TotalCommissions);
            }
        }

        private void btnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                var reportType = ((dynamic)cmbReportType.SelectedItem).Value.ToString();

                switch (reportType)
                {
                    case "Monthly":
                        LoadMonthlyCommissionReport();
                        break;
                    case "ByEmployee":
                        LoadEmployeeCommissionReport();
                        break;
                    case "RateAnalysis":
                        LoadRateAnalysisReport();
                        break;
                    case "Performance":
                        LoadPerformanceReport();
                        break;
                    case "Comprehensive":
                        LoadComprehensiveReport();
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadEmployeeCommissionReport()
        {
            // سيتم إضافة هذه الدالة لاحقاً
            await LoadMonthlyCommissionReport();
        }

        private async void LoadRateAnalysisReport()
        {
            // سيتم إضافة هذه الدالة لاحقاً
            await LoadMonthlyCommissionReport();
        }

        private async void LoadPerformanceReport()
        {
            // سيتم إضافة هذه الدالة لاحقاً
            await LoadMonthlyCommissionReport();
        }

        private async void LoadComprehensiveReport()
        {
            // سيتم إضافة هذه الدالة لاحقاً
            await LoadMonthlyCommissionReport();
        }

        private void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    FileName = $"CommissionReport_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToExcel(saveDialog.FileName);
                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToExcel(string fileName)
        {
            using (var package = new OfficeOpenXml.ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("تقرير العمولات");

                var data = (System.Collections.IList)dgvCommissionReport.DataSource;
                if (data != null && data.Count > 0)
                {
                    for (int i = 0; i < dgvCommissionReport.Columns.Count; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = dgvCommissionReport.Columns[i].HeaderText;
                    }

                    for (int i = 0; i < data.Count; i++)
                    {
                        var item = data[i];
                        var properties = item.GetType().GetProperties();

                        for (int j = 0; j < properties.Length; j++)
                        {
                            worksheet.Cells[i + 2, j + 1].Value = properties[j].GetValue(item);
                        }
                    }
                }

                package.SaveAs(new System.IO.FileInfo(fileName));
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("تم إرسال التقرير للطباعة", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cmbEmployee_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnGenerateReport_Click(sender, e);
        }
    }
}
            this.Name = "CommissionReportsForm";
            this.Text = "تقارير البرسنتاج";
            this.ResumeLayout(false);
        }
    }
}
