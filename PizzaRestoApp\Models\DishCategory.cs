using System;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج فئة الطبق
    /// Dish Category Model
    /// </summary>
    public class DishCategory
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameFr { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   !string.IsNullOrWhiteSpace(NameFr);
        }
    }
}
