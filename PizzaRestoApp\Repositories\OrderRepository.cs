using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PizzaRestoApp.Models;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع الطلبات
    /// Order Repository
    /// </summary>
    public class OrderRepository : BaseRepository<Order>
    {
        public OrderRepository() : base("orders") { }

        /// <summary>
        /// إضافة طلب جديد
        /// Add new order
        /// </summary>
        public override async Task<int> AddAsync(Order order)
        {
            var sql = @"
                INSERT INTO orders (order_number, order_type, customer_id, table_id, delivery_address,
                                  employee_id, shift_id, order_date, subtotal, tax_amount, 
                                  discount_amount, total_amount, payment_method, status, notes, 
                                  created_at, updated_at)
                VALUES (@OrderNumber, @OrderType, @CustomerId, @TableId, @DeliveryAddress,
                        @EmployeeId, @ShiftId, @OrderDate, @Subtotal, @TaxAmount, 
                        @DiscountAmount, @TotalAmount, @PaymentMethod, @Status, @Notes, 
                        @CreatedAt, @UpdatedAt);
                SELECT LAST_INSERT_ID();";

            order.CreatedAt = DateTime.Now;
            order.UpdatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, order);
        }

        /// <summary>
        /// تحديث طلب موجود
        /// Update existing order
        /// </summary>
        public override async Task<bool> UpdateAsync(Order order)
        {
            var sql = @"
                UPDATE orders 
                SET order_number = @OrderNumber, order_type = @OrderType, customer_id = @CustomerId,
                    table_id = @TableId, delivery_address = @DeliveryAddress, employee_id = @EmployeeId,
                    shift_id = @ShiftId, order_date = @OrderDate, subtotal = @Subtotal, 
                    tax_amount = @TaxAmount, discount_amount = @DiscountAmount, 
                    total_amount = @TotalAmount, payment_method = @PaymentMethod, 
                    status = @Status, notes = @Notes, updated_at = @UpdatedAt
                WHERE id = @Id";

            order.UpdatedAt = DateTime.Now;
            var affectedRows = await ExecuteAsync(sql, order);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على طلب مع جميع التفاصيل
        /// Get order with all details
        /// </summary>
        public async Task<Order?> GetByIdWithDetailsAsync(int id)
        {
            var sql = @"
                SELECT o.*, 
                       c.name as CustomerName, c.phone as CustomerPhone,
                       t.table_number as TableNumber,
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                LEFT JOIN restaurant_tables t ON o.table_id = t.id
                INNER JOIN employees e ON o.employee_id = e.id
                WHERE o.id = @Id";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Order, Customer, RestaurantTable, Employee, Order>(
                sql,
                (order, customer, table, employee) =>
                {
                    order.Customer = customer;
                    order.Table = table;
                    order.Employee = employee;
                    return order;
                },
                new { Id = id },
                splitOn: "CustomerName,TableNumber,EmployeeFirstName"
            );

            var orderResult = result.FirstOrDefault();
            if (orderResult != null)
            {
                orderResult.OrderDetails = (await GetOrderDetailsAsync(id)).ToList();
            }

            return orderResult;
        }

        /// <summary>
        /// الحصول على تفاصيل الطلب
        /// Get order details
        /// </summary>
        public async Task<IEnumerable<OrderDetail>> GetOrderDetailsAsync(int orderId)
        {
            var sql = @"
                SELECT od.*, d.name as DishName, d.name_fr as DishNameFr
                FROM order_details od
                INNER JOIN dishes d ON od.dish_id = d.id
                WHERE od.order_id = @OrderId";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<OrderDetail, Dish, OrderDetail>(
                sql,
                (orderDetail, dish) =>
                {
                    orderDetail.Dish = dish;
                    return orderDetail;
                },
                new { OrderId = orderId },
                splitOn: "DishName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على الطلبات حسب الحالة
        /// Get orders by status
        /// </summary>
        public async Task<IEnumerable<Order>> GetByStatusAsync(OrderStatus status)
        {
            var sql = @"
                SELECT o.*, 
                       c.name as CustomerName, c.phone as CustomerPhone,
                       t.table_number as TableNumber,
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                LEFT JOIN restaurant_tables t ON o.table_id = t.id
                INNER JOIN employees e ON o.employee_id = e.id
                WHERE o.status = @Status
                ORDER BY o.order_date";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Order, Customer, RestaurantTable, Employee, Order>(
                sql,
                (order, customer, table, employee) =>
                {
                    order.Customer = customer;
                    order.Table = table;
                    order.Employee = employee;
                    return order;
                },
                new { Status = status },
                splitOn: "CustomerName,TableNumber,EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على طلبات المطبخ (جديدة وتحت التحضير)
        /// Get kitchen orders (new and preparing)
        /// </summary>
        public async Task<IEnumerable<Order>> GetKitchenOrdersAsync()
        {
            var sql = @"
                SELECT o.*, 
                       c.name as CustomerName, c.phone as CustomerPhone,
                       t.table_number as TableNumber,
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                LEFT JOIN restaurant_tables t ON o.table_id = t.id
                INNER JOIN employees e ON o.employee_id = e.id
                WHERE o.status IN (@NewStatus, @PreparingStatus)
                ORDER BY o.order_date";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Order, Customer, RestaurantTable, Employee, Order>(
                sql,
                (order, customer, table, employee) =>
                {
                    order.Customer = customer;
                    order.Table = table;
                    order.Employee = employee;
                    return order;
                },
                new { NewStatus = OrderStatus.New, PreparingStatus = OrderStatus.Preparing },
                splitOn: "CustomerName,TableNumber,EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على الطلبات حسب الشيفت
        /// Get orders by shift
        /// </summary>
        public async Task<IEnumerable<Order>> GetByShiftAsync(int shiftId)
        {
            var sql = @"
                SELECT o.*, 
                       c.name as CustomerName, c.phone as CustomerPhone,
                       t.table_number as TableNumber,
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                LEFT JOIN restaurant_tables t ON o.table_id = t.id
                INNER JOIN employees e ON o.employee_id = e.id
                WHERE o.shift_id = @ShiftId
                ORDER BY o.order_date";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Order, Customer, RestaurantTable, Employee, Order>(
                sql,
                (order, customer, table, employee) =>
                {
                    order.Customer = customer;
                    order.Table = table;
                    order.Employee = employee;
                    return order;
                },
                new { ShiftId = shiftId },
                splitOn: "CustomerName,TableNumber,EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على الطلبات حسب التاريخ
        /// Get orders by date
        /// </summary>
        public async Task<IEnumerable<Order>> GetByDateAsync(DateTime date)
        {
            var sql = @"
                SELECT o.*, 
                       c.name as CustomerName, c.phone as CustomerPhone,
                       t.table_number as TableNumber,
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.id
                LEFT JOIN restaurant_tables t ON o.table_id = t.id
                INNER JOIN employees e ON o.employee_id = e.id
                WHERE DATE(o.order_date) = @Date
                ORDER BY o.order_date";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Order, Customer, RestaurantTable, Employee, Order>(
                sql,
                (order, customer, table, employee) =>
                {
                    order.Customer = customer;
                    order.Table = table;
                    order.Employee = employee;
                    return order;
                },
                new { Date = date.Date },
                splitOn: "CustomerName,TableNumber,EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// تحديث حالة الطلب
        /// Update order status
        /// </summary>
        public async Task<bool> UpdateStatusAsync(int orderId, OrderStatus status)
        {
            var sql = @"
                UPDATE orders 
                SET status = @Status, updated_at = @UpdatedAt
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, new 
            { 
                Id = orderId, 
                Status = status, 
                UpdatedAt = DateTime.Now 
            });
            
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على رقم الطلب التالي
        /// Get next order number
        /// </summary>
        public async Task<string> GetNextOrderNumberAsync()
        {
            var today = DateTime.Now.ToString("yyyyMMdd");
            var sql = @"
                SELECT COUNT(*) + 1 
                FROM orders 
                WHERE DATE(order_date) = CURDATE()";

            var orderCount = await QueryFirstOrDefaultAsync<int>(sql);
            return $"ORD-{today}-{orderCount:D4}";
        }
    }
}
