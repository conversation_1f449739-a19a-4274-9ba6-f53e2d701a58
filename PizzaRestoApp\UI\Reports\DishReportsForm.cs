using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.UI.Reports
{
    /// <summary>
    /// شاشة تقارير الأطباق
    /// Dish Reports Form
    /// </summary>
    public partial class DishReportsForm : Form
    {
        private readonly DishRepository _dishRepository;
        private readonly OrderRepository _orderRepository;
        private readonly DishCategoryRepository _categoryRepository;

        public DishReportsForm()
        {
            InitializeComponent();
            _dishRepository = new DishRepository();
            _orderRepository = new OrderRepository();
            _categoryRepository = new DishCategoryRepository();

            this.Text = "تقارير الأطباق - Dish Reports";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            InitializeReports();
        }

        private async void InitializeReports()
        {
            // إعداد التواريخ الافتراضية
            dtpFromDate.Value = DateTime.Now.Date.AddDays(-30);
            dtpToDate.Value = DateTime.Now.Date;

            // تحميل الفئات
            var categories = await _categoryRepository.GetActiveCategoriesAsync();
            cmbCategory.DataSource = categories.ToList();
            cmbCategory.DisplayMember = "Name";
            cmbCategory.ValueMember = "Id";
            cmbCategory.SelectedIndex = -1;

            // إعداد أنواع التقارير
            cmbReportType.Items.Add(new { Text = "الأطباق الأكثر مبيعاً", Value = "TopSelling" });
            cmbReportType.Items.Add(new { Text = "تحليل الربحية", Value = "Profitability" });
            cmbReportType.Items.Add(new { Text = "تقرير الفئات", Value = "Categories" });
            cmbReportType.Items.Add(new { Text = "أداء الأطباق", Value = "Performance" });
            cmbReportType.Items.Add(new { Text = "تقرير شامل", Value = "Comprehensive" });
            cmbReportType.DisplayMember = "Text";
            cmbReportType.ValueMember = "Value";
            cmbReportType.SelectedIndex = 0;

            // إعداد Chart
            SetupChart();

            // تحميل التقرير الافتراضي
            LoadTopSellingDishesReport();
        }

        private void SetupChart()
        {
            chartDish.Series.Clear();
            chartDish.ChartAreas.Clear();

            var chartArea = new ChartArea("DishArea");
            chartArea.AxisX.Title = "الأطباق";
            chartArea.AxisY.Title = "الكمية/المبلغ";
            chartArea.AxisX.LabelStyle.Angle = -45;
            chartDish.ChartAreas.Add(chartArea);

            var series = new Series("البيانات");
            series.ChartType = SeriesChartType.Column;
            series.Color = Color.SteelBlue;
            chartDish.Series.Add(series);
        }

        private async void LoadTopSellingDishesReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var topDishes = await _orderRepository.GetTopSellingDishesAsync(fromDate, toDate, 20);

                if (cmbCategory.SelectedValue != null)
                {
                    var categoryId = (int)cmbCategory.SelectedValue;
                    topDishes = topDishes.Where(d => d.CategoryId == categoryId);
                }

                // إحصائيات الأطباق الأكثر مبيعاً
                var totalQuantitySold = topDishes.Sum(d => d.QuantitySold);
                var totalRevenue = topDishes.Sum(d => d.TotalRevenue);
                var avgQuantityPerDish = topDishes.Count() > 0 ? totalQuantitySold / topDishes.Count() : 0;
                var topDish = topDishes.FirstOrDefault();

                lblTotalQuantitySold.Text = $"إجمالي الكمية المباعة: {totalQuantitySold}";
                lblTotalRevenue.Text = $"إجمالي الإيرادات: {totalRevenue:C}";
                lblAvgQuantityPerDish.Text = $"متوسط الكمية لكل طبق: {avgQuantityPerDish}";
                lblTopDish.Text = $"الطبق الأكثر مبيعاً: {topDish?.DishName ?? "لا يوجد"}";

                // تحميل تفاصيل الأطباق
                var dishDetails = topDishes.Select(d => new
                {
                    DishName = d.DishName,
                    CategoryName = d.CategoryName ?? "غير محدد",
                    QuantitySold = d.QuantitySold,
                    TotalRevenue = d.TotalRevenue,
                    AveragePrice = d.QuantitySold > 0 ? d.TotalRevenue / d.QuantitySold : 0,
                    Percentage = d.Percentage,
                    Rank = topDishes.ToList().IndexOf(d) + 1
                }).ToList();

                dgvDishReport.DataSource = dishDetails;

                // تحديث الرسم البياني
                UpdateTopSellingChart(topDishes.Take(10));

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير الأطباق الأكثر مبيعاً: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadProfitabilityReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var dishes = await _dishRepository.GetAllWithCategoriesAsync();
                var profitabilityData = new List<dynamic>();

                foreach (var dish in dishes)
                {
                    if (cmbCategory.SelectedValue != null && dish.CategoryId != (int)cmbCategory.SelectedValue)
                        continue;

                    var dishSales = await _orderRepository.GetDishSalesAsync(dish.Id, fromDate, toDate);
                    var quantitySold = dishSales.Sum(ds => ds.Quantity);
                    var totalRevenue = dishSales.Sum(ds => ds.TotalPrice);
                    var totalCost = quantitySold * dish.BaseCost;
                    var grossProfit = totalRevenue - totalCost;
                    var profitMargin = totalRevenue > 0 ? (grossProfit / totalRevenue) * 100 : 0;

                    if (quantitySold > 0) // فقط الأطباق التي تم بيعها
                    {
                        profitabilityData.Add(new
                        {
                            DishName = dish.Name,
                            CategoryName = dish.Category?.Name ?? "غير محدد",
                            QuantitySold = quantitySold,
                            TotalRevenue = totalRevenue,
                            TotalCost = totalCost,
                            GrossProfit = grossProfit,
                            ProfitMargin = profitMargin,
                            BaseCost = dish.BaseCost,
                            SellingPrice = dish.SellingPrice,
                            ProfitPerUnit = dish.SellingPrice - dish.BaseCost
                        });
                    }
                }

                var sortedData = profitabilityData.OrderByDescending(p => (decimal)p.GrossProfit).ToList();
                dgvDishReport.DataSource = sortedData;

                // إحصائيات الربحية
                var totalGrossProfit = sortedData.Sum(p => (decimal)p.GrossProfit);
                var avgProfitMargin = sortedData.Count > 0 ? sortedData.Average(p => (decimal)p.ProfitMargin) : 0;
                var mostProfitableDish = sortedData.FirstOrDefault();
                var leastProfitableDish = sortedData.LastOrDefault();

                lblTotalGrossProfit.Text = $"إجمالي الربح الإجمالي: {totalGrossProfit:C}";
                lblAvgProfitMargin.Text = $"متوسط هامش الربح: {avgProfitMargin:F1}%";
                lblMostProfitableDish.Text = $"الطبق الأكثر ربحاً: {mostProfitableDish?.DishName ?? "لا يوجد"}";
                lblLeastProfitableDish.Text = $"الطبق الأقل ربحاً: {leastProfitableDish?.DishName ?? "لا يوجد"}";

                // تحديث الرسم البياني
                UpdateProfitabilityChart(sortedData.Take(10));

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير الربحية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadCategoriesReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var categories = await _categoryRepository.GetActiveCategoriesAsync();
                var categoryData = new List<dynamic>();

                foreach (var category in categories)
                {
                    var categoryDishes = await _dishRepository.GetDishesByCategoryAsync(category.Id);
                    var totalQuantitySold = 0;
                    var totalRevenue = 0m;
                    var totalCost = 0m;

                    foreach (var dish in categoryDishes)
                    {
                        var dishSales = await _orderRepository.GetDishSalesAsync(dish.Id, fromDate, toDate);
                        var quantitySold = dishSales.Sum(ds => ds.Quantity);
                        totalQuantitySold += quantitySold;
                        totalRevenue += dishSales.Sum(ds => ds.TotalPrice);
                        totalCost += quantitySold * dish.BaseCost;
                    }

                    if (totalQuantitySold > 0)
                    {
                        categoryData.Add(new
                        {
                            CategoryName = category.Name,
                            CategoryNameFr = category.NameFr,
                            DishCount = categoryDishes.Count(),
                            TotalQuantitySold = totalQuantitySold,
                            TotalRevenue = totalRevenue,
                            TotalCost = totalCost,
                            GrossProfit = totalRevenue - totalCost,
                            ProfitMargin = totalRevenue > 0 ? ((totalRevenue - totalCost) / totalRevenue) * 100 : 0,
                            AvgRevenuePerDish = categoryDishes.Count() > 0 ? totalRevenue / categoryDishes.Count() : 0
                        });
                    }
                }

                var sortedCategoryData = categoryData.OrderByDescending(c => (decimal)c.TotalRevenue).ToList();
                dgvDishReport.DataSource = sortedCategoryData;

                // إحصائيات الفئات
                var totalCategoryRevenue = sortedCategoryData.Sum(c => (decimal)c.TotalRevenue);
                var avgRevenuePerCategory = sortedCategoryData.Count > 0 ? totalCategoryRevenue / sortedCategoryData.Count : 0;
                var topCategory = sortedCategoryData.FirstOrDefault();
                var totalDishesInCategories = sortedCategoryData.Sum(c => (int)c.DishCount);

                lblTotalCategoryRevenue.Text = $"إجمالي إيرادات الفئات: {totalCategoryRevenue:C}";
                lblAvgRevenuePerCategory.Text = $"متوسط الإيرادات لكل فئة: {avgRevenuePerCategory:C}";
                lblTopCategory.Text = $"الفئة الأكثر مبيعاً: {topCategory?.CategoryName ?? "لا يوجد"}";
                lblTotalDishesInCategories.Text = $"إجمالي الأطباق: {totalDishesInCategories}";

                // تحديث الرسم البياني
                UpdateCategoriesChart(sortedCategoryData);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير الفئات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadPerformanceReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var dishes = await _dishRepository.GetAllWithCategoriesAsync();
                var performanceData = new List<dynamic>();

                foreach (var dish in dishes)
                {
                    if (cmbCategory.SelectedValue != null && dish.CategoryId != (int)cmbCategory.SelectedValue)
                        continue;

                    var dishSales = await _orderRepository.GetDishSalesAsync(dish.Id, fromDate, toDate);
                    var quantitySold = dishSales.Sum(ds => ds.Quantity);
                    var totalRevenue = dishSales.Sum(ds => ds.TotalPrice);
                    var orderCount = dishSales.Count();

                    // حساب معدل الطلب (كم مرة تم طلب الطبق)
                    var totalOrders = await _orderRepository.GetOrdersByDateRangeAsync(fromDate, toDate);
                    var ordersWithDish = totalOrders.Where(o => o.OrderDetails.Any(od => od.DishId == dish.Id)).Count();
                    var orderFrequency = totalOrders.Count() > 0 ? (decimal)ordersWithDish / totalOrders.Count() * 100 : 0;

                    // حساب متوسط وقت التحضير
                    var avgPreparationTime = dish.PreparationTime;

                    // تقييم الأداء
                    var performanceScore = CalculatePerformanceScore(quantitySold, totalRevenue, orderFrequency, dish.SellingPrice - dish.BaseCost);

                    if (quantitySold > 0)
                    {
                        performanceData.Add(new
                        {
                            DishName = dish.Name,
                            CategoryName = dish.Category?.Name ?? "غير محدد",
                            QuantitySold = quantitySold,
                            TotalRevenue = totalRevenue,
                            OrderCount = orderCount,
                            OrderFrequency = orderFrequency,
                            AvgPreparationTime = avgPreparationTime,
                            ProfitPerUnit = dish.SellingPrice - dish.BaseCost,
                            PerformanceScore = performanceScore,
                            Status = GetPerformanceStatus(performanceScore)
                        });
                    }
                }

                var sortedPerformanceData = performanceData.OrderByDescending(p => (decimal)p.PerformanceScore).ToList();
                dgvDishReport.DataSource = sortedPerformanceData;

                // إحصائيات الأداء
                var avgPerformanceScore = sortedPerformanceData.Count > 0 ? sortedPerformanceData.Average(p => (decimal)p.PerformanceScore) : 0;
                var topPerformer = sortedPerformanceData.FirstOrDefault();
                var lowPerformers = sortedPerformanceData.Where(p => (decimal)p.PerformanceScore < 50).Count();
                var highPerformers = sortedPerformanceData.Where(p => (decimal)p.PerformanceScore >= 80).Count();

                lblAvgPerformanceScore.Text = $"متوسط نقاط الأداء: {avgPerformanceScore:F1}";
                lblTopPerformer.Text = $"الطبق الأفضل أداءً: {topPerformer?.DishName ?? "لا يوجد"}";
                lblLowPerformers.Text = $"أطباق ضعيفة الأداء: {lowPerformers}";
                lblHighPerformers.Text = $"أطباق عالية الأداء: {highPerformers}";

                // تحديث الرسم البياني
                UpdatePerformanceChart(sortedPerformanceData.Take(10));

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير الأداء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private decimal CalculatePerformanceScore(int quantitySold, decimal totalRevenue, decimal orderFrequency, decimal profitPerUnit)
        {
            // خوارزمية بسيطة لحساب نقاط الأداء
            var quantityScore = Math.Min(quantitySold * 2, 40); // حد أقصى 40 نقطة للكمية
            var revenueScore = Math.Min((double)(totalRevenue / 100), 30); // حد أقصى 30 نقطة للإيرادات
            var frequencyScore = Math.Min((double)orderFrequency, 20); // حد أقصى 20 نقطة لتكرار الطلب
            var profitScore = Math.Min((double)(profitPerUnit * 2), 10); // حد أقصى 10 نقاط للربح

            return (decimal)(quantityScore + revenueScore + frequencyScore + profitScore);
        }

        private string GetPerformanceStatus(decimal score)
        {
            if (score >= 80) return "ممتاز";
            if (score >= 60) return "جيد";
            if (score >= 40) return "متوسط";
            return "ضعيف";
        }

        private void UpdateTopSellingChart(IEnumerable<dynamic> topDishes)
        {
            chartDish.Series["البيانات"].Points.Clear();

            foreach (var dish in topDishes)
            {
                chartDish.Series["البيانات"].Points.AddXY(dish.DishName, dish.QuantitySold);
            }
        }

        private void UpdateProfitabilityChart(List<dynamic> profitabilityData)
        {
            chartDish.Series["البيانات"].Points.Clear();

            foreach (var item in profitabilityData)
            {
                chartDish.Series["البيانات"].Points.AddXY(item.DishName, item.GrossProfit);
            }
        }

        private void UpdateCategoriesChart(List<dynamic> categoryData)
        {
            chartDish.Series["البيانات"].Points.Clear();
            chartDish.Series["البيانات"].ChartType = SeriesChartType.Pie;

            foreach (var item in categoryData)
            {
                chartDish.Series["البيانات"].Points.AddXY(item.CategoryName, item.TotalRevenue);
            }
        }

        private void UpdatePerformanceChart(List<dynamic> performanceData)
        {
            chartDish.Series["البيانات"].Points.Clear();
            chartDish.Series["البيانات"].ChartType = SeriesChartType.Column;

            foreach (var item in performanceData)
            {
                chartDish.Series["البيانات"].Points.AddXY(item.DishName, item.PerformanceScore);
            }
        }

        private async void btnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                var reportType = ((dynamic)cmbReportType.SelectedItem).Value.ToString();

                switch (reportType)
                {
                    case "TopSelling":
                        await LoadTopSellingDishesReport();
                        break;
                    case "Profitability":
                        await LoadProfitabilityReport();
                        break;
                    case "Categories":
                        await LoadCategoriesReport();
                        break;
                    case "Performance":
                        await LoadPerformanceReport();
                        break;
                    case "Comprehensive":
                        await LoadComprehensiveReport();
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadComprehensiveReport()
        {
            // تقرير شامل يجمع كل المعلومات
            await LoadTopSellingDishesReport();
        }

        private void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    FileName = $"DishReport_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToExcel(saveDialog.FileName);
                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToExcel(string fileName)
        {
            using (var package = new OfficeOpenXml.ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("تقرير الأطباق");

                var data = (System.Collections.IList)dgvDishReport.DataSource;
                if (data != null && data.Count > 0)
                {
                    for (int i = 0; i < dgvDishReport.Columns.Count; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = dgvDishReport.Columns[i].HeaderText;
                    }

                    for (int i = 0; i < data.Count; i++)
                    {
                        var item = data[i];
                        var properties = item.GetType().GetProperties();

                        for (int j = 0; j < properties.Length; j++)
                        {
                            worksheet.Cells[i + 2, j + 1].Value = properties[j].GetValue(item);
                        }
                    }
                }

                package.SaveAs(new System.IO.FileInfo(fileName));
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("تم إرسال التقرير للطباعة", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cmbCategory_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnGenerateReport_Click(sender, e);
        }
    }
}
