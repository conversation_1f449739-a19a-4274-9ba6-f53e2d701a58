using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع العملاء
    /// Customer Repository
    /// </summary>
    public class CustomerRepository : BaseRepository<Customer>
    {
        public CustomerRepository() : base("customers") { }

        /// <summary>
        /// إضافة عميل جديد
        /// Add new customer
        /// </summary>
        public override async Task<int> AddAsync(Customer customer)
        {
            var sql = @"
                INSERT INTO customers (name, phone, email, address, created_at)
                VALUES (@Name, @Phone, @Email, @Address, @CreatedAt);
                SELECT LAST_INSERT_ID();";

            customer.CreatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, customer);
        }

        /// <summary>
        /// تحديث عميل موجود
        /// Update existing customer
        /// </summary>
        public override async Task<bool> UpdateAsync(Customer customer)
        {
            var sql = @"
                UPDATE customers 
                SET name = @Name, phone = @Phone, email = @Email, address = @Address
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, customer);
            return affectedRows > 0;
        }

        /// <summary>
        /// البحث عن عميل برقم الهاتف
        /// Search customer by phone
        /// </summary>
        public async Task<Customer?> GetByPhoneAsync(string phone)
        {
            var sql = "SELECT * FROM customers WHERE phone = @Phone";
            return await QueryFirstOrDefaultAsync(sql, new { Phone = phone });
        }

        /// <summary>
        /// البحث في العملاء
        /// Search customers
        /// </summary>
        public async Task<IEnumerable<Customer>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT * FROM customers 
                WHERE name LIKE @SearchTerm OR phone LIKE @SearchTerm OR email LIKE @SearchTerm
                ORDER BY name";

            var searchPattern = $"%{searchTerm}%";
            return await QueryAsync(sql, new { SearchTerm = searchPattern });
        }
    }
}
