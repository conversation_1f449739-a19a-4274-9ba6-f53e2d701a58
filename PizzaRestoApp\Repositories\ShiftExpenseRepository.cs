using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع مصروفات الشيفت
    /// Shift Expense Repository
    /// </summary>
    public class ShiftExpenseRepository : BaseRepository<ShiftExpense>
    {
        public ShiftExpenseRepository() : base("shift_expenses") { }

        /// <summary>
        /// إضافة مصروف شيفت جديد
        /// Add new shift expense
        /// </summary>
        public override async Task<int> AddAsync(ShiftExpense expense)
        {
            var sql = @"
                INSERT INTO shift_expenses (shift_id, description, amount, expense_date, 
                                          employee_id, created_at)
                VALUES (@ShiftId, @Description, @Amount, @ExpenseDate, 
                        @EmployeeId, @CreatedAt);
                SELECT LAST_INSERT_ID();";

            expense.CreatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, expense);
        }

        /// <summary>
        /// تحديث مصروف شيفت موجود
        /// Update existing shift expense
        /// </summary>
        public override async Task<bool> UpdateAsync(ShiftExpense expense)
        {
            var sql = @"
                UPDATE shift_expenses 
                SET description = @Description, amount = @Amount, expense_date = @ExpenseDate
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, expense);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على مصروفات الشيفت
        /// Get shift expenses
        /// </summary>
        public async Task<IEnumerable<ShiftExpense>> GetByShiftIdAsync(int shiftId)
        {
            var sql = @"
                SELECT se.*, 
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM shift_expenses se
                INNER JOIN employees e ON se.employee_id = e.id
                WHERE se.shift_id = @ShiftId
                ORDER BY se.expense_date DESC";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<ShiftExpense, Employee, ShiftExpense>(
                sql,
                (expense, employee) =>
                {
                    expense.Employee = employee;
                    return expense;
                },
                new { ShiftId = shiftId },
                splitOn: "EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على إجمالي مصروفات الشيفت
        /// Get total shift expenses
        /// </summary>
        public async Task<decimal> GetTotalByShiftIdAsync(int shiftId)
        {
            var sql = "SELECT COALESCE(SUM(amount), 0) FROM shift_expenses WHERE shift_id = @ShiftId";
            return await QueryFirstOrDefaultAsync<decimal>(sql, new { ShiftId = shiftId });
        }

        /// <summary>
        /// الحصول على مصروفات الموظف حسب التاريخ
        /// Get employee expenses by date range
        /// </summary>
        public async Task<IEnumerable<ShiftExpense>> GetByEmployeeAndDateRangeAsync(int employeeId, DateTime startDate, DateTime endDate)
        {
            var sql = @"
                SELECT se.*, 
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName,
                       s.shift_number as ShiftNumber
                FROM shift_expenses se
                INNER JOIN employees e ON se.employee_id = e.id
                INNER JOIN shifts s ON se.shift_id = s.id
                WHERE se.employee_id = @EmployeeId 
                  AND DATE(se.expense_date) BETWEEN @StartDate AND @EndDate
                ORDER BY se.expense_date DESC";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<ShiftExpense, Employee, Shift, ShiftExpense>(
                sql,
                (expense, employee, shift) =>
                {
                    expense.Employee = employee;
                    expense.Shift = shift;
                    return expense;
                },
                new { EmployeeId = employeeId, StartDate = startDate.Date, EndDate = endDate.Date },
                splitOn: "EmployeeFirstName,ShiftNumber"
            );

            return result;
        }

        /// <summary>
        /// الحصول على المصروفات حسب التاريخ
        /// Get expenses by date range
        /// </summary>
        public async Task<IEnumerable<ShiftExpense>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            var sql = @"
                SELECT se.*, 
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName,
                       s.shift_number as ShiftNumber
                FROM shift_expenses se
                INNER JOIN employees e ON se.employee_id = e.id
                INNER JOIN shifts s ON se.shift_id = s.id
                WHERE DATE(se.expense_date) BETWEEN @StartDate AND @EndDate
                ORDER BY se.expense_date DESC";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<ShiftExpense, Employee, Shift, ShiftExpense>(
                sql,
                (expense, employee, shift) =>
                {
                    expense.Employee = employee;
                    expense.Shift = shift;
                    return expense;
                },
                new { StartDate = startDate.Date, EndDate = endDate.Date },
                splitOn: "EmployeeFirstName,ShiftNumber"
            );

            return result;
        }

        /// <summary>
        /// حذف جميع مصروفات الشيفت
        /// Delete all shift expenses
        /// </summary>
        public async Task<bool> DeleteByShiftIdAsync(int shiftId)
        {
            var sql = "DELETE FROM shift_expenses WHERE shift_id = @ShiftId";
            var affectedRows = await ExecuteAsync(sql, new { ShiftId = shiftId });
            return affectedRows > 0;
        }
    }
}
