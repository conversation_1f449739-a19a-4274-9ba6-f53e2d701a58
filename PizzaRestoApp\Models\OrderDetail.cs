using System;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج تفاصيل الطلب
    /// Order Detail Model
    /// </summary>
    public class OrderDetail
    {
        public int Id { get; set; }
        public int OrderId { get; set; }
        public int DishId { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
        public string? SpecialNotes { get; set; }
        public DateTime CreatedAt { get; set; }

        // Navigation Properties
        public Order? Order { get; set; }
        public Dish? Dish { get; set; }

        /// <summary>
        /// حساب السعر الإجمالي
        /// Calculate total price
        /// </summary>
        public decimal CalculateTotalPrice()
        {
            return Quantity * UnitPrice;
        }

        /// <summary>
        /// تحديث السعر الإجمالي
        /// Update total price
        /// </summary>
        public void UpdateTotalPrice()
        {
            TotalPrice = CalculateTotalPrice();
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            return OrderId > 0 &&
                   DishId > 0 &&
                   Quantity > 0 &&
                   UnitPrice >= 0;
        }
    }
}
