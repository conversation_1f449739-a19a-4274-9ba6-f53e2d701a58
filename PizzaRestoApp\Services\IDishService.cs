using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Services
{
    /// <summary>
    /// واجهة خدمة الأطباق
    /// Dish Service Interface
    /// </summary>
    public interface IDishService
    {
        /// <summary>
        /// الحصول على جميع الأطباق
        /// Get all dishes
        /// </summary>
        Task<IEnumerable<Dish>> GetAllDishesAsync();

        /// <summary>
        /// الحصول على طبق بالمعرف
        /// Get dish by ID
        /// </summary>
        Task<Dish?> GetDishByIdAsync(int id);

        /// <summary>
        /// الحصول على الأطباق المتاحة
        /// Get available dishes
        /// </summary>
        Task<IEnumerable<Dish>> GetAvailableDishesAsync();

        /// <summary>
        /// الحصول على الأطباق حسب الفئة
        /// Get dishes by category
        /// </summary>
        Task<IEnumerable<Dish>> GetDishesByCategoryAsync(int categoryId);

        /// <summary>
        /// إضافة طبق جديد
        /// Add new dish
        /// </summary>
        Task<int> AddDishAsync(Dish dish);

        /// <summary>
        /// تحديث طبق
        /// Update dish
        /// </summary>
        Task<bool> UpdateDishAsync(Dish dish);

        /// <summary>
        /// حذف طبق
        /// Delete dish
        /// </summary>
        Task<bool> DeleteDishAsync(int id);

        /// <summary>
        /// إضافة مكون للطبق
        /// Add ingredient to dish
        /// </summary>
        Task<bool> AddIngredientToDishAsync(int dishId, int ingredientId, decimal quantity);

        /// <summary>
        /// إزالة مكون من الطبق
        /// Remove ingredient from dish
        /// </summary>
        Task<bool> RemoveIngredientFromDishAsync(int dishId, int ingredientId);

        /// <summary>
        /// تحديث كمية المكون في الطبق
        /// Update ingredient quantity in dish
        /// </summary>
        Task<bool> UpdateDishIngredientQuantityAsync(int dishId, int ingredientId, decimal quantity);

        /// <summary>
        /// حساب تكلفة الطبق تلقائياً
        /// Calculate dish cost automatically
        /// </summary>
        Task<decimal> CalculateDishCostAsync(int dishId);

        /// <summary>
        /// تحديث أسعار الطبق
        /// Update dish prices
        /// </summary>
        Task<bool> UpdateDishPricesAsync(int dishId);

        /// <summary>
        /// التحقق من توفر مكونات الطبق
        /// Check dish ingredients availability
        /// </summary>
        Task<bool> CheckDishAvailabilityAsync(int dishId);

        /// <summary>
        /// تحديث توفر الطبق
        /// Update dish availability
        /// </summary>
        Task<bool> UpdateDishAvailabilityAsync(int dishId, bool isAvailable);

        /// <summary>
        /// البحث في الأطباق
        /// Search dishes
        /// </summary>
        Task<IEnumerable<Dish>> SearchDishesAsync(string searchTerm);

        /// <summary>
        /// التحقق من صحة بيانات الطبق
        /// Validate dish data
        /// </summary>
        Task<(bool IsValid, string ErrorMessage)> ValidateDishAsync(Dish dish);
    }
}
