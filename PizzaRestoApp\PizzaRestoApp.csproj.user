﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Compile Update="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Kitchen\KitchenDisplayForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Management\DishesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Management\EmployeesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Management\IngredientsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Management\ManagementMainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Management\PurchasesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Management\SettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Management\SuppliersForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Management\TablesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\POS\POSMainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\POS\ShiftManagementForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Reports\EmployeeReportsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Reports\InventoryReportsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Reports\ReportsMainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Update="UI\Reports\SalesReportsForm.cs">
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
</Project>