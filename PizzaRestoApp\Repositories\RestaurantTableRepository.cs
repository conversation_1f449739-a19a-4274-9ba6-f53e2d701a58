using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع طاولات المطعم
    /// Restaurant Table Repository
    /// </summary>
    public class RestaurantTableRepository : BaseRepository<RestaurantTable>
    {
        public RestaurantTableRepository() : base("restaurant_tables") { }

        /// <summary>
        /// إضافة طاولة جديدة
        /// Add new table
        /// </summary>
        public override async Task<int> AddAsync(RestaurantTable table)
        {
            var sql = @"
                INSERT INTO restaurant_tables (table_number, capacity, location, is_active, created_at)
                VALUES (@TableNumber, @Capacity, @Location, @IsActive, @CreatedAt);
                SELECT LAST_INSERT_ID();";

            table.CreatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, table);
        }

        /// <summary>
        /// تحديث طاولة موجودة
        /// Update existing table
        /// </summary>
        public override async Task<bool> UpdateAsync(RestaurantTable table)
        {
            var sql = @"
                UPDATE restaurant_tables 
                SET table_number = @TableNumber, capacity = @Capacity, 
                    location = @Location, is_active = @IsActive
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, table);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على الطاولات النشطة
        /// Get active tables
        /// </summary>
        public async Task<IEnumerable<RestaurantTable>> GetActiveTablesAsync()
        {
            var sql = "SELECT * FROM restaurant_tables WHERE is_active = 1 ORDER BY table_number";
            return await QueryAsync(sql);
        }

        /// <summary>
        /// الحصول على طاولة برقم الطاولة
        /// Get table by table number
        /// </summary>
        public async Task<RestaurantTable?> GetByTableNumberAsync(string tableNumber)
        {
            var sql = "SELECT * FROM restaurant_tables WHERE table_number = @TableNumber";
            return await QueryFirstOrDefaultAsync(sql, new { TableNumber = tableNumber });
        }

        /// <summary>
        /// الحصول على الطاولات حسب السعة
        /// Get tables by capacity
        /// </summary>
        public async Task<IEnumerable<RestaurantTable>> GetByCapacityAsync(int minCapacity)
        {
            var sql = @"
                SELECT * FROM restaurant_tables 
                WHERE is_active = 1 AND capacity >= @MinCapacity 
                ORDER BY capacity, table_number";
            
            return await QueryAsync(sql, new { MinCapacity = minCapacity });
        }
    }
}
