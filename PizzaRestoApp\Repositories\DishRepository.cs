using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع الأطباق
    /// Dish Repository
    /// </summary>
    public class DishRepository : BaseRepository<Dish>
    {
        public DishRepository() : base("dishes") { }

        /// <summary>
        /// إضافة طبق جديد
        /// Add new dish
        /// </summary>
        public override async Task<int> AddAsync(Dish dish)
        {
            var sql = @"
                INSERT INTO dishes (name, name_fr, category_id, description, base_cost, 
                                  profit_margin, selling_price, preparation_time, is_available, 
                                  is_active, created_at, updated_at)
                VALUES (@Name, @NameFr, @CategoryId, @Description, @BaseCost, 
                        @ProfitMargin, @SellingPrice, @PreparationTime, @IsAvailable, 
                        @IsActive, @CreatedAt, @UpdatedAt);
                SELECT LAST_INSERT_ID();";

            dish.CreatedAt = DateTime.Now;
            dish.UpdatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, dish);
        }

        /// <summary>
        /// تحديث طبق موجود
        /// Update existing dish
        /// </summary>
        public override async Task<bool> UpdateAsync(Dish dish)
        {
            var sql = @"
                UPDATE dishes 
                SET name = @Name, name_fr = @NameFr, category_id = @CategoryId, 
                    description = @Description, base_cost = @BaseCost, profit_margin = @ProfitMargin,
                    selling_price = @SellingPrice, preparation_time = @PreparationTime, 
                    is_available = @IsAvailable, is_active = @IsActive, updated_at = @UpdatedAt
                WHERE id = @Id";

            dish.UpdatedAt = DateTime.Now;
            var affectedRows = await ExecuteAsync(sql, dish);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على طبق مع تفاصيل الفئة
        /// Get dish with category details
        /// </summary>
        public async Task<Dish?> GetByIdWithCategoryAsync(int id)
        {
            var sql = @"
                SELECT d.*, c.name as CategoryName, c.name_fr as CategoryNameFr, 
                       c.description as CategoryDescription
                FROM dishes d
                INNER JOIN dish_categories c ON d.category_id = c.id
                WHERE d.id = @Id";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Dish, DishCategory, Dish>(
                sql,
                (dish, category) =>
                {
                    dish.Category = category;
                    return dish;
                },
                new { Id = id },
                splitOn: "CategoryName"
            );

            return result.FirstOrDefault();
        }

        /// <summary>
        /// الحصول على طبق مع المكونات
        /// Get dish with ingredients
        /// </summary>
        public async Task<Dish?> GetByIdWithIngredientsAsync(int id)
        {
            var dish = await GetByIdWithCategoryAsync(id);
            if (dish != null)
            {
                dish.Ingredients = (await GetDishIngredientsAsync(id)).ToList();
            }
            return dish;
        }

        /// <summary>
        /// الحصول على مكونات الطبق
        /// Get dish ingredients
        /// </summary>
        public async Task<IEnumerable<DishIngredient>> GetDishIngredientsAsync(int dishId)
        {
            var sql = @"
                SELECT di.*, i.name as IngredientName, i.name_fr as IngredientNameFr,
                       i.cost_per_unit as IngredientCostPerUnit, i.current_stock as IngredientCurrentStock,
                       u.name as UnitName, u.abbreviation as UnitAbbreviation
                FROM dish_ingredients di
                INNER JOIN ingredients i ON di.ingredient_id = i.id
                INNER JOIN units u ON i.unit_id = u.id
                WHERE di.dish_id = @DishId";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<DishIngredient, Ingredient, Unit, DishIngredient>(
                sql,
                (dishIngredient, ingredient, unit) =>
                {
                    ingredient.Unit = unit;
                    dishIngredient.Ingredient = ingredient;
                    return dishIngredient;
                },
                new { DishId = dishId },
                splitOn: "IngredientName,UnitName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على جميع الأطباق مع الفئات
        /// Get all dishes with categories
        /// </summary>
        public async Task<IEnumerable<Dish>> GetAllWithCategoriesAsync()
        {
            var sql = @"
                SELECT d.*, c.name as CategoryName, c.name_fr as CategoryNameFr, 
                       c.description as CategoryDescription
                FROM dishes d
                INNER JOIN dish_categories c ON d.category_id = c.id
                ORDER BY c.name, d.name";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Dish, DishCategory, Dish>(
                sql,
                (dish, category) =>
                {
                    dish.Category = category;
                    return dish;
                },
                splitOn: "CategoryName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على الأطباق المتاحة
        /// Get available dishes
        /// </summary>
        public async Task<IEnumerable<Dish>> GetAvailableDishesAsync()
        {
            var sql = @"
                SELECT d.*, c.name as CategoryName, c.name_fr as CategoryNameFr, 
                       c.description as CategoryDescription
                FROM dishes d
                INNER JOIN dish_categories c ON d.category_id = c.id
                WHERE d.is_available = 1 AND d.is_active = 1 AND c.is_active = 1
                ORDER BY c.name, d.name";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Dish, DishCategory, Dish>(
                sql,
                (dish, category) =>
                {
                    dish.Category = category;
                    return dish;
                },
                splitOn: "CategoryName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على الأطباق حسب الفئة
        /// Get dishes by category
        /// </summary>
        public async Task<IEnumerable<Dish>> GetByCategoryAsync(int categoryId)
        {
            var sql = @"
                SELECT d.*, c.name as CategoryName, c.name_fr as CategoryNameFr, 
                       c.description as CategoryDescription
                FROM dishes d
                INNER JOIN dish_categories c ON d.category_id = c.id
                WHERE d.category_id = @CategoryId AND d.is_active = 1
                ORDER BY d.name";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Dish, DishCategory, Dish>(
                sql,
                (dish, category) =>
                {
                    dish.Category = category;
                    return dish;
                },
                new { CategoryId = categoryId },
                splitOn: "CategoryName"
            );

            return result;
        }

        /// <summary>
        /// البحث في الأطباق
        /// Search dishes
        /// </summary>
        public async Task<IEnumerable<Dish>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT d.*, c.name as CategoryName, c.name_fr as CategoryNameFr, 
                       c.description as CategoryDescription
                FROM dishes d
                INNER JOIN dish_categories c ON d.category_id = c.id
                WHERE d.is_active = 1 AND (d.name LIKE @SearchTerm OR d.name_fr LIKE @SearchTerm)
                ORDER BY d.name";

            var searchPattern = $"%{searchTerm}%";
            
            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Dish, DishCategory, Dish>(
                sql,
                (dish, category) =>
                {
                    dish.Category = category;
                    return dish;
                },
                new { SearchTerm = searchPattern },
                splitOn: "CategoryName"
            );

            return result;
        }

        /// <summary>
        /// تحديث توفر الطبق
        /// Update dish availability
        /// </summary>
        public async Task<bool> UpdateAvailabilityAsync(int dishId, bool isAvailable)
        {
            var sql = @"
                UPDATE dishes 
                SET is_available = @IsAvailable, updated_at = @UpdatedAt
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, new 
            { 
                Id = dishId, 
                IsAvailable = isAvailable, 
                UpdatedAt = DateTime.Now 
            });
            
            return affectedRows > 0;
        }
    }
}
