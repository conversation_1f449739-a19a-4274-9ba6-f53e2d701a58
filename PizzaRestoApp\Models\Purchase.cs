using System;
using System.Collections.Generic;
using System.Linq;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج المشتريات
    /// Purchase Model
    /// </summary>
    public class Purchase
    {
        public int Id { get; set; }
        public string PurchaseNumber { get; set; } = string.Empty;
        public int SupplierId { get; set; }
        public int EmployeeId { get; set; }
        public DateTime PurchaseDate { get; set; }
        public decimal TotalAmount { get; set; }
        public string? Notes { get; set; }
        public PurchaseStatus Status { get; set; } = PurchaseStatus.Pending;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public Supplier? Supplier { get; set; }
        public Employee? Employee { get; set; }
        public List<PurchaseDetail> PurchaseDetails { get; set; } = new List<PurchaseDetail>();

        /// <summary>
        /// حساب المجموع الإجمالي من التفاصيل
        /// Calculate total amount from details
        /// </summary>
        public decimal CalculateTotalAmount()
        {
            return PurchaseDetails?.Sum(pd => pd.TotalCost) ?? 0;
        }

        /// <summary>
        /// تحديث المجموع الإجمالي
        /// Update total amount
        /// </summary>
        public void UpdateTotalAmount()
        {
            TotalAmount = CalculateTotalAmount();
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(PurchaseNumber) &&
                   SupplierId > 0 &&
                   EmployeeId > 0 &&
                   PurchaseDate <= DateTime.Now &&
                   TotalAmount >= 0;
        }

        /// <summary>
        /// تغيير حالة المشتريات
        /// Change purchase status
        /// </summary>
        /// <param name="newStatus">الحالة الجديدة</param>
        public void ChangeStatus(PurchaseStatus newStatus)
        {
            Status = newStatus;
            UpdatedAt = DateTime.Now;
        }
    }
}
