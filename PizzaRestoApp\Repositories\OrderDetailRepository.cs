using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع تفاصيل الطلبات
    /// Order Detail Repository
    /// </summary>
    public class OrderDetailRepository : BaseRepository<OrderDetail>
    {
        public OrderDetailRepository() : base("order_details") { }

        /// <summary>
        /// إضافة تفصيل طلب جديد
        /// Add new order detail
        /// </summary>
        public override async Task<int> AddAsync(OrderDetail orderDetail)
        {
            var sql = @"
                INSERT INTO order_details (order_id, dish_id, quantity, unit_price, total_price, 
                                         special_notes, created_at)
                VALUES (@OrderId, @DishId, @Quantity, @UnitPrice, @TotalPrice, 
                        @SpecialNotes, @CreatedAt);
                SELECT LAST_INSERT_ID();";

            orderDetail.CreatedAt = DateTime.Now;
            orderDetail.UpdateTotalPrice(); // تحديث السعر الإجمالي
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, orderDetail);
        }

        /// <summary>
        /// تحديث تفصيل طلب موجود
        /// Update existing order detail
        /// </summary>
        public override async Task<bool> UpdateAsync(OrderDetail orderDetail)
        {
            var sql = @"
                UPDATE order_details 
                SET quantity = @Quantity, unit_price = @UnitPrice, total_price = @TotalPrice,
                    special_notes = @SpecialNotes
                WHERE id = @Id";

            orderDetail.UpdateTotalPrice(); // تحديث السعر الإجمالي
            var affectedRows = await ExecuteAsync(sql, orderDetail);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على تفاصيل الطلب
        /// Get order details
        /// </summary>
        public async Task<IEnumerable<OrderDetail>> GetByOrderIdAsync(int orderId)
        {
            var sql = "SELECT * FROM order_details WHERE order_id = @OrderId";
            return await QueryAsync(sql, new { OrderId = orderId });
        }

        /// <summary>
        /// حذف جميع تفاصيل الطلب
        /// Delete all order details
        /// </summary>
        public async Task<bool> DeleteByOrderIdAsync(int orderId)
        {
            var sql = "DELETE FROM order_details WHERE order_id = @OrderId";
            var affectedRows = await ExecuteAsync(sql, new { OrderId = orderId });
            return affectedRows > 0;
        }

        /// <summary>
        /// إضافة عدة تفاصيل للطلب
        /// Add multiple order details
        /// </summary>
        public async Task<bool> AddMultipleAsync(IEnumerable<OrderDetail> orderDetails)
        {
            var sql = @"
                INSERT INTO order_details (order_id, dish_id, quantity, unit_price, total_price, 
                                         special_notes, created_at)
                VALUES (@OrderId, @DishId, @Quantity, @UnitPrice, @TotalPrice, 
                        @SpecialNotes, @CreatedAt)";

            foreach (var detail in orderDetails)
            {
                detail.CreatedAt = DateTime.Now;
                detail.UpdateTotalPrice();
            }

            using var connection = CreateConnection();
            var affectedRows = await connection.ExecuteAsync(sql, orderDetails);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على إجمالي مبيعات طبق معين
        /// Get total sales for a specific dish
        /// </summary>
        public async Task<decimal> GetDishTotalSalesAsync(int dishId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var sql = @"
                SELECT COALESCE(SUM(od.total_price), 0)
                FROM order_details od
                INNER JOIN orders o ON od.order_id = o.id
                WHERE od.dish_id = @DishId";

            var parameters = new { DishId = dishId };

            if (startDate.HasValue && endDate.HasValue)
            {
                sql += " AND DATE(o.order_date) BETWEEN @StartDate AND @EndDate";
                parameters = new { DishId = dishId, StartDate = startDate.Value.Date, EndDate = endDate.Value.Date };
            }

            return await QueryFirstOrDefaultAsync<decimal>(sql, parameters);
        }

        /// <summary>
        /// الحصول على كمية مبيعات طبق معين
        /// Get total quantity sold for a specific dish
        /// </summary>
        public async Task<int> GetDishTotalQuantityAsync(int dishId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var sql = @"
                SELECT COALESCE(SUM(od.quantity), 0)
                FROM order_details od
                INNER JOIN orders o ON od.order_id = o.id
                WHERE od.dish_id = @DishId";

            var parameters = new { DishId = dishId };

            if (startDate.HasValue && endDate.HasValue)
            {
                sql += " AND DATE(o.order_date) BETWEEN @StartDate AND @EndDate";
                parameters = new { DishId = dishId, StartDate = startDate.Value.Date, EndDate = endDate.Value.Date };
            }

            return await QueryFirstOrDefaultAsync<int>(sql, parameters);
        }
    }
}
