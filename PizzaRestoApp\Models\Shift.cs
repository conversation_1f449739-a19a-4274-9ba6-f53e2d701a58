using System;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج الشيفت
    /// Shift Model
    /// </summary>
    public class Shift
    {
        public int Id { get; set; }
        public string ShiftNumber { get; set; } = string.Empty;
        public int EmployeeId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public decimal OpeningCash { get; set; } = 0;
        public decimal? ClosingCash { get; set; }
        public decimal? ExpectedCash { get; set; }
        public decimal? CashDifference { get; set; }
        public decimal TotalSales { get; set; } = 0;
        public decimal TotalExpenses { get; set; } = 0;
        public ShiftStatus Status { get; set; } = ShiftStatus.Open;
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public Employee? Employee { get; set; }

        /// <summary>
        /// مدة الشيفت بالساعات
        /// Shift duration in hours
        /// </summary>
        public decimal? DurationHours
        {
            get
            {
                if (EndTime.HasValue)
                {
                    var duration = EndTime.Value - StartTime;
                    return (decimal)duration.TotalHours;
                }
                return null;
            }
        }

        /// <summary>
        /// صافي المبيعات (المبيعات - المصروفات)
        /// Net sales (sales - expenses)
        /// </summary>
        public decimal NetSales => TotalSales - TotalExpenses;

        /// <summary>
        /// النقد المتوقع (النقد الافتتاحي + صافي المبيعات)
        /// Expected cash (opening cash + net sales)
        /// </summary>
        public decimal CalculatedExpectedCash => OpeningCash + NetSales;

        /// <summary>
        /// حساب فرق النقد
        /// Calculate cash difference
        /// </summary>
        public void CalculateCashDifference()
        {
            if (ClosingCash.HasValue)
            {
                ExpectedCash = CalculatedExpectedCash;
                CashDifference = ClosingCash.Value - ExpectedCash.Value;
            }
        }

        /// <summary>
        /// إغلاق الشيفت
        /// Close shift
        /// </summary>
        /// <param name="closingCash">النقد الفعلي عند الإغلاق</param>
        public void CloseShift(decimal closingCash)
        {
            EndTime = DateTime.Now;
            ClosingCash = closingCash;
            CalculateCashDifference();
            Status = ShiftStatus.Closed;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(ShiftNumber) &&
                   EmployeeId > 0 &&
                   StartTime <= DateTime.Now &&
                   OpeningCash >= 0;
        }
    }
}
