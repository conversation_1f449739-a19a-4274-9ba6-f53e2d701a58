using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع الموردين
    /// Supplier Repository
    /// </summary>
    public class SupplierRepository : BaseRepository<Supplier>
    {
        public SupplierRepository() : base("suppliers") { }

        /// <summary>
        /// إضافة مورد جديد
        /// Add new supplier
        /// </summary>
        public override async Task<int> AddAsync(Supplier supplier)
        {
            var sql = @"
                INSERT INTO suppliers (name, contact_person, phone, email, address, is_active, created_at)
                VALUES (@Name, @ContactPerson, @Phone, @Email, @Address, @IsActive, @CreatedAt);
                SELECT LAST_INSERT_ID();";

            supplier.CreatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, supplier);
        }

        /// <summary>
        /// تحديث مورد موجود
        /// Update existing supplier
        /// </summary>
        public override async Task<bool> UpdateAsync(Supplier supplier)
        {
            var sql = @"
                UPDATE suppliers 
                SET name = @Name, contact_person = @ContactPerson, phone = @Phone, 
                    email = @Email, address = @Address, is_active = @IsActive
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, supplier);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على الموردين النشطين
        /// Get active suppliers
        /// </summary>
        public async Task<IEnumerable<Supplier>> GetActiveSuppliersAsync()
        {
            var sql = "SELECT * FROM suppliers WHERE is_active = 1 ORDER BY name";
            return await QueryAsync(sql);
        }

        /// <summary>
        /// البحث في الموردين
        /// Search suppliers
        /// </summary>
        public async Task<IEnumerable<Supplier>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT * FROM suppliers 
                WHERE is_active = 1 AND (name LIKE @SearchTerm OR contact_person LIKE @SearchTerm OR phone LIKE @SearchTerm)
                ORDER BY name";

            var searchPattern = $"%{searchTerm}%";
            return await QueryAsync(sql, new { SearchTerm = searchPattern });
        }

        /// <summary>
        /// الحصول على مورد بالاسم
        /// Get supplier by name
        /// </summary>
        public async Task<Supplier?> GetByNameAsync(string name)
        {
            var sql = "SELECT * FROM suppliers WHERE name = @Name";
            return await QueryFirstOrDefaultAsync(sql, new { Name = name });
        }
    }
}
