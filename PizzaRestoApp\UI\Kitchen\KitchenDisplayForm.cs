using System;
using System.Linq;
using System.Windows.Forms;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.UI.Kitchen
{
    /// <summary>
    /// شاشة المطبخ
    /// Kitchen Display Form
    /// </summary>
    public partial class KitchenDisplayForm : Form
    {
        private readonly OrderRepository _orderRepository;
        private readonly System.Windows.Forms.Timer _refreshTimer;

        public KitchenDisplayForm()
        {
            InitializeComponent();
            _orderRepository = new OrderRepository();
            
            this.Text = "شاشة المطبخ - Kitchen Display";
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // إعداد Timer للتحديث التلقائي كل 10 ثوانٍ
            _refreshTimer = new System.Windows.Forms.Timer();
            _refreshTimer.Interval = Constants.KITCHEN_REFRESH_INTERVAL; // 10 seconds
            _refreshTimer.Tick += RefreshTimer_Tick;
            _refreshTimer.Start();
            
            LoadKitchenOrders();
        }

        private async void LoadKitchenOrders()
        {
            try
            {
                var orders = await _orderRepository.GetKitchenOrdersAsync();
                
                // مسح الطلبات الحالية
                panelNewOrders.Controls.Clear();
                panelPreparingOrders.Controls.Clear();
                panelReadyOrders.Controls.Clear();
                
                foreach (var order in orders)
                {
                    var orderCard = CreateOrderCard(order);
                    
                    switch (order.Status)
                    {
                        case OrderStatus.New:
                            panelNewOrders.Controls.Add(orderCard);
                            break;
                        case OrderStatus.Preparing:
                            panelPreparingOrders.Controls.Add(orderCard);
                            break;
                        case OrderStatus.Ready:
                            panelReadyOrders.Controls.Add(orderCard);
                            break;
                    }
                }
                
                // تحديث عدد الطلبات
                lblNewOrdersCount.Text = $"طلبات جديدة ({panelNewOrders.Controls.Count})";
                lblPreparingOrdersCount.Text = $"تحت التحضير ({panelPreparingOrders.Controls.Count})";
                lblReadyOrdersCount.Text = $"جاهزة ({panelReadyOrders.Controls.Count})";
                
                // تحديث وقت آخر تحديث
                lblLastUpdate.Text = $"آخر تحديث: {DateTime.Now:HH:mm:ss}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطلبات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private Panel CreateOrderCard(Order order)
        {
            var card = new Panel
            {
                Width = 280,
                Height = 200,
                BorderStyle = BorderStyle.FixedSingle,
                Margin = new Padding(5),
                BackColor = GetOrderCardColor(order.Status),
                Tag = order
            };

            // رقم الطلب
            var lblOrderNumber = new Label
            {
                Text = order.OrderNumber,
                Font = new System.Drawing.Font("Tahoma", 12, System.Drawing.FontStyle.Bold),
                Location = new System.Drawing.Point(10, 10),
                Size = new System.Drawing.Size(260, 25),
                TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            };
            card.Controls.Add(lblOrderNumber);

            // نوع الطلب
            var lblOrderType = new Label
            {
                Text = GetOrderTypeText(order.OrderType),
                Font = new System.Drawing.Font("Tahoma", 10),
                Location = new System.Drawing.Point(10, 40),
                Size = new System.Drawing.Size(100, 20)
            };
            card.Controls.Add(lblOrderType);

            // وقت الطلب
            var lblOrderTime = new Label
            {
                Text = order.OrderDate.ToString("HH:mm"),
                Font = new System.Drawing.Font("Tahoma", 10),
                Location = new System.Drawing.Point(180, 40),
                Size = new System.Drawing.Size(90, 20),
                TextAlign = System.Drawing.ContentAlignment.MiddleRight
            };
            card.Controls.Add(lblOrderTime);

            // تفاصيل الطلب
            var lblOrderDetails = new Label
            {
                Text = GetOrderDetailsText(order),
                Font = new System.Drawing.Font("Tahoma", 9),
                Location = new System.Drawing.Point(10, 70),
                Size = new System.Drawing.Size(260, 80),
                TextAlign = System.Drawing.ContentAlignment.TopRight
            };
            card.Controls.Add(lblOrderDetails);

            // أزرار التحكم
            if (order.Status == OrderStatus.New)
            {
                var btnStartPreparing = new Button
                {
                    Text = "بدء التحضير",
                    Font = new System.Drawing.Font("Tahoma", 9, System.Drawing.FontStyle.Bold),
                    Location = new System.Drawing.Point(10, 160),
                    Size = new System.Drawing.Size(100, 30),
                    BackColor = System.Drawing.Color.Orange,
                    Tag = order.Id
                };
                btnStartPreparing.Click += BtnStartPreparing_Click;
                card.Controls.Add(btnStartPreparing);
            }
            else if (order.Status == OrderStatus.Preparing)
            {
                var btnMarkReady = new Button
                {
                    Text = "جاهز",
                    Font = new System.Drawing.Font("Tahoma", 9, System.Drawing.FontStyle.Bold),
                    Location = new System.Drawing.Point(10, 160),
                    Size = new System.Drawing.Size(100, 30),
                    BackColor = System.Drawing.Color.LightGreen,
                    Tag = order.Id
                };
                btnMarkReady.Click += BtnMarkReady_Click;
                card.Controls.Add(btnMarkReady);
            }

            return card;
        }

        private System.Drawing.Color GetOrderCardColor(OrderStatus status)
        {
            return status switch
            {
                OrderStatus.New => System.Drawing.Color.LightYellow,
                OrderStatus.Preparing => System.Drawing.Color.LightBlue,
                OrderStatus.Ready => System.Drawing.Color.LightGreen,
                _ => System.Drawing.Color.White
            };
        }

        private string GetOrderTypeText(OrderType orderType)
        {
            return orderType switch
            {
                OrderType.DineIn => "في المطعم",
                OrderType.Takeaway => "تيك أواي",
                OrderType.Delivery => "توصيل",
                _ => "غير محدد"
            };
        }

        private string GetOrderDetailsText(Order order)
        {
            if (order.OrderDetails == null || !order.OrderDetails.Any())
                return "لا توجد تفاصيل";

            var details = order.OrderDetails
                .Select(od => $"• {od.Dish?.Name} × {od.Quantity}")
                .Take(4); // عرض أول 4 عناصر فقط

            var result = string.Join("\n", details);
            
            if (order.OrderDetails.Count > 4)
                result += $"\n... و {order.OrderDetails.Count - 4} عنصر آخر";

            return result;
        }

        private async void BtnStartPreparing_Click(object sender, EventArgs e)
        {
            try
            {
                var button = sender as Button;
                var orderId = (int)button.Tag;
                
                await _orderRepository.UpdateStatusAsync(orderId, OrderStatus.Preparing);
                LoadKitchenOrders();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث حالة الطلب: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnMarkReady_Click(object sender, EventArgs e)
        {
            try
            {
                var button = sender as Button;
                var orderId = (int)button.Tag;
                
                await _orderRepository.UpdateStatusAsync(orderId, OrderStatus.Ready);
                LoadKitchenOrders();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث حالة الطلب: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshTimer_Tick(object sender, EventArgs e)
        {
            LoadKitchenOrders();
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadKitchenOrders();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            _refreshTimer?.Stop();
            this.Close();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
