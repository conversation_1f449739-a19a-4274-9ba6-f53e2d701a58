using System;
using System.Configuration;
using System.Windows.Forms;
using PizzaRestoApp.Config;

namespace PizzaRestoApp.UI.Management
{
    /// <summary>
    /// شاشة الإعدادات
    /// Settings Form
    /// </summary>
    public partial class SettingsForm : Form
    {
        public SettingsForm()
        {
            InitializeComponent();
            this.Text = "الإعدادات - Settings";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            LoadSettings();
        }

        private void LoadSettings()
        {
            try
            {
                // إعدادات قاعدة البيانات
                txtServer.Text = DatabaseConfig.Server;
                txtDatabase.Text = DatabaseConfig.Database;
                txtUsername.Text = DatabaseConfig.Username;
                txtPassword.Text = DatabaseConfig.Password;
                numPort.Value = DatabaseConfig.Port;

                // إعدادات المطعم
                txtRestaurantName.Text = ConfigurationManager.AppSettings["RestaurantName"] ?? "مطعم البيتزا";
                txtRestaurantNameFr.Text = ConfigurationManager.AppSettings["RestaurantNameFr"] ?? "Pizza Restaurant";
                txtAddress.Text = ConfigurationManager.AppSettings["Address"] ?? "";
                txtPhone.Text = ConfigurationManager.AppSettings["Phone"] ?? "";
                txtEmail.Text = ConfigurationManager.AppSettings["Email"] ?? "";

                // إعدادات الضرائب والخصومات
                numTaxRate.Value = decimal.Parse(ConfigurationManager.AppSettings["TaxRate"] ?? "0");
                numServiceCharge.Value = decimal.Parse(ConfigurationManager.AppSettings["ServiceCharge"] ?? "0");
                numMaxDiscount.Value = decimal.Parse(ConfigurationManager.AppSettings["MaxDiscount"] ?? "50");

                // إعدادات النظام
                chkAutoBackup.Checked = bool.Parse(ConfigurationManager.AppSettings["AutoBackup"] ?? "false");
                numBackupInterval.Value = int.Parse(ConfigurationManager.AppSettings["BackupInterval"] ?? "24");
                chkPrintReceipts.Checked = bool.Parse(ConfigurationManager.AppSettings["PrintReceipts"] ?? "true");
                txtReceiptFooter.Text = ConfigurationManager.AppSettings["ReceiptFooter"] ?? "شكراً لزيارتكم";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateSettings()) return;

                // حفظ إعدادات قاعدة البيانات
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);

                config.ConnectionStrings.ConnectionStrings["DefaultConnection"].ConnectionString =
                    $"Server={txtServer.Text};Database={txtDatabase.Text};Uid={txtUsername.Text};Pwd={txtPassword.Text};Port={numPort.Value};";

                // حفظ إعدادات المطعم
                config.AppSettings.Settings["RestaurantName"].Value = txtRestaurantName.Text;
                config.AppSettings.Settings["RestaurantNameFr"].Value = txtRestaurantNameFr.Text;
                config.AppSettings.Settings["Address"].Value = txtAddress.Text;
                config.AppSettings.Settings["Phone"].Value = txtPhone.Text;
                config.AppSettings.Settings["Email"].Value = txtEmail.Text;

                // حفظ إعدادات الضرائب والخصومات
                config.AppSettings.Settings["TaxRate"].Value = numTaxRate.Value.ToString();
                config.AppSettings.Settings["ServiceCharge"].Value = numServiceCharge.Value.ToString();
                config.AppSettings.Settings["MaxDiscount"].Value = numMaxDiscount.Value.ToString();

                // حفظ إعدادات النظام
                config.AppSettings.Settings["AutoBackup"].Value = chkAutoBackup.Checked.ToString();
                config.AppSettings.Settings["BackupInterval"].Value = numBackupInterval.Value.ToString();
                config.AppSettings.Settings["PrintReceipts"].Value = chkPrintReceipts.Checked.ToString();
                config.AppSettings.Settings["ReceiptFooter"].Value = txtReceiptFooter.Text;

                config.Save(ConfigurationSaveMode.Modified);
                ConfigurationManager.RefreshSection("appSettings");
                ConfigurationManager.RefreshSection("connectionStrings");

                MessageBox.Show("تم حفظ الإعدادات بنجاح\nيرجى إعادة تشغيل التطبيق لتطبيق التغييرات", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnTestConnection_Click(object sender, EventArgs e)
        {
            try
            {
                var connectionString = $"Server={txtServer.Text};Database={txtDatabase.Text};Uid={txtUsername.Text};Pwd={txtPassword.Text};Port={numPort.Value};";

                using (var connection = new MySql.Data.MySqlClient.MySqlConnection(connectionString))
                {
                    connection.Open();
                    MessageBox.Show("تم الاتصال بقاعدة البيانات بنجاح", "نجح الاتصال",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل الاتصال بقاعدة البيانات: {ex.Message}", "خطأ في الاتصال",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnBackup_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "SQL Files (*.sql)|*.sql",
                    FileName = $"backup_{DateTime.Now:yyyyMMdd_HHmmss}.sql"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    // هنا يمكن إضافة كود النسخ الاحتياطي
                    MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnRestore_Click(object sender, EventArgs e)
        {
            try
            {
                var openDialog = new OpenFileDialog
                {
                    Filter = "SQL Files (*.sql)|*.sql"
                };

                if (openDialog.ShowDialog() == DialogResult.OK)
                {
                    if (MessageBox.Show("هل أنت متأكد من استعادة النسخة الاحتياطية؟\nسيتم استبدال البيانات الحالية",
                        "تأكيد الاستعادة", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                    {
                        // هنا يمكن إضافة كود استعادة النسخة الاحتياطية
                        MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استعادة النسخة الاحتياطية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateSettings()
        {
            if (string.IsNullOrWhiteSpace(txtServer.Text))
            {
                MessageBox.Show("يرجى إدخال عنوان الخادم", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtServer.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtDatabase.Text))
            {
                MessageBox.Show("يرجى إدخال اسم قاعدة البيانات", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDatabase.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtRestaurantName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المطعم", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRestaurantName.Focus();
                return false;
            }

            return true;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟",
                "تأكيد إعادة التعيين", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                LoadSettings();
            }
        }
    }
}
