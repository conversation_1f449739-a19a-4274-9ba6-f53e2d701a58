using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Services
{
    /// <summary>
    /// واجهة خدمة الموظفين
    /// Employee Service Interface
    /// </summary>
    public interface IEmployeeService
    {
        /// <summary>
        /// الحصول على جميع الموظفين
        /// Get all employees
        /// </summary>
        Task<IEnumerable<Employee>> GetAllEmployeesAsync();

        /// <summary>
        /// الحصول على موظف بالمعرف
        /// Get employee by ID
        /// </summary>
        Task<Employee?> GetEmployeeByIdAsync(int id);

        /// <summary>
        /// الحصول على الموظفين النشطين
        /// Get active employees
        /// </summary>
        Task<IEnumerable<Employee>> GetActiveEmployeesAsync();

        /// <summary>
        /// الحصول على الموظفين حسب الدور
        /// Get employees by role
        /// </summary>
        Task<IEnumerable<Employee>> GetEmployeesByRoleAsync(string roleName);

        /// <summary>
        /// إضافة موظف جديد
        /// Add new employee
        /// </summary>
        Task<int> AddEmployeeAsync(Employee employee);

        /// <summary>
        /// تحديث موظف
        /// Update employee
        /// </summary>
        Task<bool> UpdateEmployeeAsync(Employee employee);

        /// <summary>
        /// حذف موظف
        /// Delete employee
        /// </summary>
        Task<bool> DeleteEmployeeAsync(int id);

        /// <summary>
        /// التحقق من صحة بيانات الموظف
        /// Validate employee data
        /// </summary>
        Task<(bool IsValid, string ErrorMessage)> ValidateEmployeeAsync(Employee employee);

        /// <summary>
        /// إنشاء كود موظف تلقائي
        /// Generate automatic employee code
        /// </summary>
        Task<string> GenerateEmployeeCodeAsync();

        /// <summary>
        /// تسجيل دخول الموظف
        /// Employee check-in
        /// </summary>
        Task<bool> CheckInEmployeeAsync(int employeeId, string? notes = null);

        /// <summary>
        /// تسجيل خروج الموظف
        /// Employee check-out
        /// </summary>
        Task<bool> CheckOutEmployeeAsync(int employeeId, string? notes = null);

        /// <summary>
        /// الحصول على الموظفين الحاضرين حالياً
        /// Get currently present employees
        /// </summary>
        Task<IEnumerable<EmployeeAttendance>> GetCurrentlyPresentEmployeesAsync();
    }
}
