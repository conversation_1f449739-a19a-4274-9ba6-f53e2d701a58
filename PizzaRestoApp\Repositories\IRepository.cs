using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// واجهة المستودع الأساسية
    /// Base Repository Interface
    /// </summary>
    /// <typeparam name="T">نوع الكائن</typeparam>
    public interface IRepository<T> where T : class
    {
        /// <summary>
        /// الحصول على جميع السجلات
        /// Get all records
        /// </summary>
        Task<IEnumerable<T>> GetAllAsync();

        /// <summary>
        /// الحصول على سجل بالمعرف
        /// Get record by ID
        /// </summary>
        Task<T?> GetByIdAsync(int id);

        /// <summary>
        /// إضافة سجل جديد
        /// Add new record
        /// </summary>
        Task<int> AddAsync(T entity);

        /// <summary>
        /// تحديث سجل موجود
        /// Update existing record
        /// </summary>
        Task<bool> UpdateAsync(T entity);

        /// <summary>
        /// حذف سجل
        /// Delete record
        /// </summary>
        Task<bool> DeleteAsync(int id);

        /// <summary>
        /// التحقق من وجود سجل
        /// Check if record exists
        /// </summary>
        Task<bool> ExistsAsync(int id);

        /// <summary>
        /// عدد السجلات
        /// Count records
        /// </summary>
        Task<int> CountAsync();
    }
}
