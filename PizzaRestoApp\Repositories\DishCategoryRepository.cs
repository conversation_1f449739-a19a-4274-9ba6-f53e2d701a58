using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع فئات الأطباق
    /// Dish Category Repository
    /// </summary>
    public class DishCategoryRepository : BaseRepository<DishCategory>
    {
        public DishCategoryRepository() : base("dish_categories") { }

        /// <summary>
        /// إضافة فئة طبق جديدة
        /// Add new dish category
        /// </summary>
        public override async Task<int> AddAsync(DishCategory category)
        {
            var sql = @"
                INSERT INTO dish_categories (name, name_fr, description, is_active, created_at)
                VALUES (@Name, @NameFr, @Description, @IsActive, @CreatedAt);
                SELECT LAST_INSERT_ID();";

            category.CreatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, category);
        }

        /// <summary>
        /// تحديث فئة طبق موجودة
        /// Update existing dish category
        /// </summary>
        public override async Task<bool> UpdateAsync(DishCategory category)
        {
            var sql = @"
                UPDATE dish_categories 
                SET name = @Name, name_fr = @NameFr, description = @Description, is_active = @IsActive
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, category);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على الفئات النشطة
        /// Get active categories
        /// </summary>
        public async Task<IEnumerable<DishCategory>> GetActiveCategoriesAsync()
        {
            var sql = "SELECT * FROM dish_categories WHERE is_active = 1 ORDER BY name";
            return await QueryAsync(sql);
        }

        /// <summary>
        /// الحصول على فئة بالاسم
        /// Get category by name
        /// </summary>
        public async Task<DishCategory?> GetByNameAsync(string name)
        {
            var sql = "SELECT * FROM dish_categories WHERE name = @Name";
            return await QueryFirstOrDefaultAsync(sql, new { Name = name });
        }
    }
}
