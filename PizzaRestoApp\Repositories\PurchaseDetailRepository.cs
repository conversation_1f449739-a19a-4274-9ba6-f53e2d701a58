using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع تفاصيل المشتريات
    /// Purchase Detail Repository
    /// </summary>
    public class PurchaseDetailRepository : BaseRepository<PurchaseDetail>
    {
        public PurchaseDetailRepository() : base("purchase_details") { }

        /// <summary>
        /// إضافة تفصيل مشتريات جديد
        /// Add new purchase detail
        /// </summary>
        public override async Task<int> AddAsync(PurchaseDetail purchaseDetail)
        {
            var sql = @"
                INSERT INTO purchase_details (purchase_id, ingredient_id, quantity, unit_cost, 
                                            total_cost, created_at)
                VALUES (@PurchaseId, @IngredientId, @Quantity, @UnitCost, 
                        @TotalCost, @CreatedAt);
                SELECT LAST_INSERT_ID();";

            purchaseDetail.CreatedAt = DateTime.Now;
            purchaseDetail.UpdateTotalCost(); // تحديث التكلفة الإجمالية
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, purchaseDetail);
        }

        /// <summary>
        /// تحديث تفصيل مشتريات موجود
        /// Update existing purchase detail
        /// </summary>
        public override async Task<bool> UpdateAsync(PurchaseDetail purchaseDetail)
        {
            var sql = @"
                UPDATE purchase_details 
                SET quantity = @Quantity, unit_cost = @UnitCost, total_cost = @TotalCost
                WHERE id = @Id";

            purchaseDetail.UpdateTotalCost(); // تحديث التكلفة الإجمالية
            var affectedRows = await ExecuteAsync(sql, purchaseDetail);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على تفاصيل المشتريات
        /// Get purchase details
        /// </summary>
        public async Task<IEnumerable<PurchaseDetail>> GetByPurchaseIdAsync(int purchaseId)
        {
            var sql = "SELECT * FROM purchase_details WHERE purchase_id = @PurchaseId";
            return await QueryAsync(sql, new { PurchaseId = purchaseId });
        }

        /// <summary>
        /// حذف جميع تفاصيل المشتريات
        /// Delete all purchase details
        /// </summary>
        public async Task<bool> DeleteByPurchaseIdAsync(int purchaseId)
        {
            var sql = "DELETE FROM purchase_details WHERE purchase_id = @PurchaseId";
            var affectedRows = await ExecuteAsync(sql, new { PurchaseId = purchaseId });
            return affectedRows > 0;
        }

        /// <summary>
        /// إضافة عدة تفاصيل للمشتريات
        /// Add multiple purchase details
        /// </summary>
        public async Task<bool> AddMultipleAsync(IEnumerable<PurchaseDetail> purchaseDetails)
        {
            var sql = @"
                INSERT INTO purchase_details (purchase_id, ingredient_id, quantity, unit_cost, 
                                            total_cost, created_at)
                VALUES (@PurchaseId, @IngredientId, @Quantity, @UnitCost, 
                        @TotalCost, @CreatedAt)";

            foreach (var detail in purchaseDetails)
            {
                detail.CreatedAt = DateTime.Now;
                detail.UpdateTotalCost();
            }

            using var connection = CreateConnection();
            var affectedRows = await connection.ExecuteAsync(sql, purchaseDetails);
            return affectedRows > 0;
        }
    }
}
