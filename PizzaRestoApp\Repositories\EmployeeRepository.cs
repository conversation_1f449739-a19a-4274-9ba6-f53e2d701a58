using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع الموظفين
    /// Employee Repository
    /// </summary>
    public class EmployeeRepository : BaseRepository<Employee>
    {
        public EmployeeRepository() : base("employees") { }

        /// <summary>
        /// إضافة موظف جديد
        /// Add new employee
        /// </summary>
        public override async Task<int> AddAsync(Employee employee)
        {
            var sql = @"
                INSERT INTO employees (employee_code, first_name, last_name, phone, email, 
                                     role_id, hire_date, salary, percentage_rate, is_active, 
                                     created_at, updated_at)
                VALUES (@EmployeeCode, @FirstName, @LastName, @Phone, @Email, 
                        @RoleId, @HireDate, @Salary, @PercentageRate, @IsActive, 
                        @CreatedAt, @UpdatedAt);
                SELECT LAST_INSERT_ID();";

            employee.CreatedAt = DateTime.Now;
            employee.UpdatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, employee);
        }

        /// <summary>
        /// تحديث موظف موجود
        /// Update existing employee
        /// </summary>
        public override async Task<bool> UpdateAsync(Employee employee)
        {
            var sql = @"
                UPDATE employees 
                SET employee_code = @EmployeeCode, first_name = @FirstName, last_name = @LastName,
                    phone = @Phone, email = @Email, role_id = @RoleId, hire_date = @HireDate,
                    salary = @Salary, percentage_rate = @PercentageRate, is_active = @IsActive,
                    updated_at = @UpdatedAt
                WHERE id = @Id";

            employee.UpdatedAt = DateTime.Now;
            var affectedRows = await ExecuteAsync(sql, employee);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على موظف مع تفاصيل الدور
        /// Get employee with role details
        /// </summary>
        public async Task<Employee?> GetByIdWithRoleAsync(int id)
        {
            var sql = @"
                SELECT e.*, r.name as RoleName, r.name_fr as RoleNameFr, r.description as RoleDescription
                FROM employees e
                INNER JOIN roles r ON e.role_id = r.id
                WHERE e.id = @Id";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Employee, Role, Employee>(
                sql,
                (employee, role) =>
                {
                    employee.Role = role;
                    return employee;
                },
                new { Id = id },
                splitOn: "RoleName"
            );

            return result.FirstOrDefault();
        }

        /// <summary>
        /// الحصول على جميع الموظفين مع تفاصيل الأدوار
        /// Get all employees with role details
        /// </summary>
        public async Task<IEnumerable<Employee>> GetAllWithRolesAsync()
        {
            var sql = @"
                SELECT e.*, r.name as RoleName, r.name_fr as RoleNameFr, r.description as RoleDescription
                FROM employees e
                INNER JOIN roles r ON e.role_id = r.id
                ORDER BY e.first_name, e.last_name";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Employee, Role, Employee>(
                sql,
                (employee, role) =>
                {
                    employee.Role = role;
                    return employee;
                },
                splitOn: "RoleName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على موظف بكود الموظف
        /// Get employee by employee code
        /// </summary>
        public async Task<Employee?> GetByEmployeeCodeAsync(string employeeCode)
        {
            var sql = "SELECT * FROM employees WHERE employee_code = @EmployeeCode";
            return await QueryFirstOrDefaultAsync(sql, new { EmployeeCode = employeeCode });
        }

        /// <summary>
        /// الحصول على الموظفين النشطين
        /// Get active employees
        /// </summary>
        public async Task<IEnumerable<Employee>> GetActiveEmployeesAsync()
        {
            var sql = @"
                SELECT e.*, r.name as RoleName, r.name_fr as RoleNameFr, r.description as RoleDescription
                FROM employees e
                INNER JOIN roles r ON e.role_id = r.id
                WHERE e.is_active = 1
                ORDER BY e.first_name, e.last_name";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Employee, Role, Employee>(
                sql,
                (employee, role) =>
                {
                    employee.Role = role;
                    return employee;
                },
                splitOn: "RoleName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على الموظفين حسب الدور
        /// Get employees by role
        /// </summary>
        public async Task<IEnumerable<Employee>> GetByRoleAsync(string roleName)
        {
            var sql = @"
                SELECT e.*, r.name as RoleName, r.name_fr as RoleNameFr, r.description as RoleDescription
                FROM employees e
                INNER JOIN roles r ON e.role_id = r.id
                WHERE r.name = @RoleName AND e.is_active = 1
                ORDER BY e.first_name, e.last_name";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Employee, Role, Employee>(
                sql,
                (employee, role) =>
                {
                    employee.Role = role;
                    return employee;
                },
                new { RoleName = roleName },
                splitOn: "RoleName"
            );

            return result;
        }
    }
}
