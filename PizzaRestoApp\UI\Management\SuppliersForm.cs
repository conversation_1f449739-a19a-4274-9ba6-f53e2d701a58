using System;
using System.Linq;
using System.Windows.Forms;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;

namespace PizzaRestoApp.UI.Management
{
    /// <summary>
    /// شاشة إدارة الموردين
    /// Suppliers Management Form
    /// </summary>
    public partial class SuppliersForm : Form
    {
        private readonly SupplierRepository _supplierRepository;

        public SuppliersForm()
        {
            InitializeComponent();
            _supplierRepository = new SupplierRepository();

            this.Text = "إدارة الموردين - Suppliers Management";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            LoadSuppliers();
        }

        private async void LoadSuppliers()
        {
            try
            {
                var suppliers = await _supplierRepository.GetAllAsync();
                dgvSuppliers.DataSource = suppliers.ToList();

                // تخصيص أعمدة DataGridView
                if (dgvSuppliers.Columns.Count > 0)
                {
                    dgvSuppliers.Columns["Id"].HeaderText = "المعرف";
                    dgvSuppliers.Columns["Name"].HeaderText = "اسم المورد";
                    dgvSuppliers.Columns["ContactPerson"].HeaderText = "الشخص المسؤول";
                    dgvSuppliers.Columns["Phone"].HeaderText = "الهاتف";
                    dgvSuppliers.Columns["Email"].HeaderText = "البريد الإلكتروني";
                    dgvSuppliers.Columns["Address"].HeaderText = "العنوان";
                    dgvSuppliers.Columns["IsActive"].HeaderText = "نشط";

                    // إخفاء الأعمدة غير المطلوبة
                    dgvSuppliers.Columns["CreatedAt"].Visible = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearForm()
        {
            txtName.Clear();
            txtContactPerson.Clear();
            txtPhone.Clear();
            txtEmail.Clear();
            txtAddress.Clear();
            chkIsActive.Checked = true;

            btnAdd.Enabled = true;
            btnUpdate.Enabled = false;
            btnDelete.Enabled = false;
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;

                var supplier = new Supplier
                {
                    Name = txtName.Text.Trim(),
                    ContactPerson = txtContactPerson.Text.Trim(),
                    Phone = txtPhone.Text.Trim(),
                    Email = txtEmail.Text.Trim(),
                    Address = txtAddress.Text.Trim(),
                    IsActive = chkIsActive.Checked
                };

                await _supplierRepository.AddAsync(supplier);
                MessageBox.Show("تم إضافة المورد بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                ClearForm();
                await LoadSuppliers();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المورد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;
                if (dgvSuppliers.CurrentRow == null) return;

                var supplierId = (int)dgvSuppliers.CurrentRow.Cells["Id"].Value;
                var supplier = await _supplierRepository.GetByIdAsync(supplierId);

                if (supplier != null)
                {
                    supplier.Name = txtName.Text.Trim();
                    supplier.ContactPerson = txtContactPerson.Text.Trim();
                    supplier.Phone = txtPhone.Text.Trim();
                    supplier.Email = txtEmail.Text.Trim();
                    supplier.Address = txtAddress.Text.Trim();
                    supplier.IsActive = chkIsActive.Checked;

                    await _supplierRepository.UpdateAsync(supplier);
                    MessageBox.Show("تم تحديث المورد بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    ClearForm();
                    await LoadSuppliers();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث المورد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvSuppliers.CurrentRow == null) return;

                if (MessageBox.Show("هل أنت متأكد من حذف هذا المورد؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    var supplierId = (int)dgvSuppliers.CurrentRow.Cells["Id"].Value;
                    await _supplierRepository.DeleteAsync(supplierId);

                    MessageBox.Show("تم حذف المورد بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    ClearForm();
                    await LoadSuppliers();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المورد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dgvSuppliers_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvSuppliers.CurrentRow != null)
            {
                var row = dgvSuppliers.CurrentRow;

                txtName.Text = row.Cells["Name"].Value?.ToString() ?? "";
                txtContactPerson.Text = row.Cells["ContactPerson"].Value?.ToString() ?? "";
                txtPhone.Text = row.Cells["Phone"].Value?.ToString() ?? "";
                txtEmail.Text = row.Cells["Email"].Value?.ToString() ?? "";
                txtAddress.Text = row.Cells["Address"].Value?.ToString() ?? "";
                chkIsActive.Checked = Convert.ToBoolean(row.Cells["IsActive"].Value);

                btnAdd.Enabled = false;
                btnUpdate.Enabled = true;
                btnDelete.Enabled = true;
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المورد", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtContactPerson.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الشخص المسؤول", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtContactPerson.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPhone.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الهاتف", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPhone.Focus();
                return false;
            }

            return true;
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private async void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    await LoadSuppliers();
                    return;
                }

                var suppliers = await _supplierRepository.SearchAsync(txtSearch.Text.Trim());
                dgvSuppliers.DataSource = suppliers.ToList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
