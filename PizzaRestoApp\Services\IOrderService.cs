using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PizzaRestoApp.Models;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.Services
{
    /// <summary>
    /// واجهة خدمة الطلبات
    /// Order Service Interface
    /// </summary>
    public interface IOrderService
    {
        /// <summary>
        /// الحصول على جميع الطلبات
        /// Get all orders
        /// </summary>
        Task<IEnumerable<Order>> GetAllOrdersAsync();

        /// <summary>
        /// الحصول على طلب بالمعرف
        /// Get order by ID
        /// </summary>
        Task<Order?> GetOrderByIdAsync(int id);

        /// <summary>
        /// الحصول على الطلبات حسب الحالة
        /// Get orders by status
        /// </summary>
        Task<IEnumerable<Order>> GetOrdersByStatusAsync(OrderStatus status);

        /// <summary>
        /// الحصول على طلبات المطبخ
        /// Get kitchen orders
        /// </summary>
        Task<IEnumerable<Order>> GetKitchenOrdersAsync();

        /// <summary>
        /// الحصول على الطلبات حسب الشيفت
        /// Get orders by shift
        /// </summary>
        Task<IEnumerable<Order>> GetOrdersByShiftAsync(int shiftId);

        /// <summary>
        /// إنشاء طلب جديد
        /// Create new order
        /// </summary>
        Task<int> CreateOrderAsync(Order order, IEnumerable<OrderDetail> orderDetails);

        /// <summary>
        /// تحديث طلب
        /// Update order
        /// </summary>
        Task<bool> UpdateOrderAsync(Order order);

        /// <summary>
        /// تحديث حالة الطلب
        /// Update order status
        /// </summary>
        Task<bool> UpdateOrderStatusAsync(int orderId, OrderStatus status);

        /// <summary>
        /// إلغاء طلب
        /// Cancel order
        /// </summary>
        Task<bool> CancelOrderAsync(int orderId, string reason);

        /// <summary>
        /// حساب مبالغ الطلب
        /// Calculate order amounts
        /// </summary>
        Task<(decimal Subtotal, decimal Tax, decimal Total)> CalculateOrderAmountsAsync(IEnumerable<OrderDetail> orderDetails, decimal discountAmount = 0);

        /// <summary>
        /// التحقق من إمكانية تحضير الطلب
        /// Check if order can be prepared
        /// </summary>
        Task<bool> CanOrderBePreparedAsync(int orderId);

        /// <summary>
        /// خصم المكونات من المخزون
        /// Deduct ingredients from stock
        /// </summary>
        Task<bool> DeductIngredientsFromStockAsync(int orderId, int employeeId);

        /// <summary>
        /// إرجاع المكونات للمخزون (في حالة الإلغاء)
        /// Return ingredients to stock (in case of cancellation)
        /// </summary>
        Task<bool> ReturnIngredientsToStockAsync(int orderId, int employeeId);

        /// <summary>
        /// حساب عمولة الموظف للطلب
        /// Calculate employee commission for order
        /// </summary>
        Task<bool> CalculateEmployeeCommissionAsync(int orderId);

        /// <summary>
        /// إنشاء رقم طلب تلقائي
        /// Generate automatic order number
        /// </summary>
        Task<string> GenerateOrderNumberAsync();

        /// <summary>
        /// التحقق من صحة بيانات الطلب
        /// Validate order data
        /// </summary>
        Task<(bool IsValid, string ErrorMessage)> ValidateOrderAsync(Order order, IEnumerable<OrderDetail> orderDetails);

        /// <summary>
        /// الحصول على تقرير مبيعات يومي
        /// Get daily sales report
        /// </summary>
        Task<dynamic> GetDailySalesReportAsync(DateTime date);

        /// <summary>
        /// الحصول على تقرير مبيعات شهري
        /// Get monthly sales report
        /// </summary>
        Task<dynamic> GetMonthlySalesReportAsync(int year, int month);
    }
}
