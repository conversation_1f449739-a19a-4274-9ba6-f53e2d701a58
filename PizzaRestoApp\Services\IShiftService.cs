using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Services
{
    /// <summary>
    /// واجهة خدمة الشيفتات
    /// Shift Service Interface
    /// </summary>
    public interface IShiftService
    {
        /// <summary>
        /// فتح شيفت جديد
        /// Open new shift
        /// </summary>
        Task<int> OpenShiftAsync(int employeeId, decimal openingCash);

        /// <summary>
        /// إغلاق الشيفت
        /// Close shift
        /// </summary>
        Task<bool> CloseShiftAsync(int shiftId, decimal closingCash, string? notes = null);

        /// <summary>
        /// الحصول على الشيفت المفتوح للموظف
        /// Get open shift for employee
        /// </summary>
        Task<Shift?> GetOpenShiftByEmployeeAsync(int employeeId);

        /// <summary>
        /// الحصول على شيفت بالمعرف
        /// Get shift by ID
        /// </summary>
        Task<Shift?> GetShiftByIdAsync(int id);

        /// <summary>
        /// الحصول على الشيفتات حسب التاريخ
        /// Get shifts by date
        /// </summary>
        Task<IEnumerable<Shift>> GetShiftsByDateAsync(DateTime date);

        /// <summary>
        /// إضافة مصروف للشيفت
        /// Add expense to shift
        /// </summary>
        Task<bool> AddShiftExpenseAsync(int shiftId, string description, decimal amount, int employeeId);

        /// <summary>
        /// تحديث مبيعات الشيفت
        /// Update shift sales
        /// </summary>
        Task<bool> UpdateShiftSalesAsync(int shiftId);

        /// <summary>
        /// تحديث مصروفات الشيفت
        /// Update shift expenses
        /// </summary>
        Task<bool> UpdateShiftExpensesAsync(int shiftId);

        /// <summary>
        /// الحصول على تقرير الشيفت
        /// Get shift report
        /// </summary>
        Task<dynamic> GetShiftReportAsync(int shiftId);

        /// <summary>
        /// التحقق من وجود شيفت مفتوح
        /// Check if there's an open shift
        /// </summary>
        Task<bool> HasOpenShiftAsync(int employeeId);

        /// <summary>
        /// إنشاء رقم شيفت تلقائي
        /// Generate automatic shift number
        /// </summary>
        Task<string> GenerateShiftNumberAsync();
    }
}
