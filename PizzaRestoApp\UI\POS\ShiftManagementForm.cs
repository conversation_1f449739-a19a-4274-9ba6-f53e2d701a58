using System;
using System.Linq;
using System.Windows.Forms;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;

namespace PizzaRestoApp.UI.POS
{
    /// <summary>
    /// شاشة إدارة الشيفتات
    /// Shift Management Form
    /// </summary>
    public partial class ShiftManagementForm : Form
    {
        private readonly ShiftRepository _shiftRepository;
        private readonly EmployeeRepository _employeeRepository;
        
        public Shift CurrentShift { get; private set; }

        public ShiftManagementForm()
        {
            InitializeComponent();
            
            _shiftRepository = new ShiftRepository();
            _employeeRepository = new EmployeeRepository();
            
            this.Text = "إدارة الشيفتات - Shift Management";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            LoadEmployees();
        }

        private async void LoadEmployees()
        {
            try
            {
                var employees = await _employeeRepository.GetActiveEmployeesAsync();
                var cashiers = employees.Where(e => e.Role?.Name == "cashier" || e.Role?.Name == "manager").ToList();
                
                cmbEmployee.DataSource = cashiers;
                cmbEmployee.DisplayMember = "FullName";
                cmbEmployee.ValueMember = "Id";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموظفين: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnOpenShift_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbEmployee.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار الموظف", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var employeeId = (int)cmbEmployee.SelectedValue;
                
                // التحقق من عدم وجود شيفت مفتوح للموظف
                var existingShift = await _shiftRepository.GetOpenShiftByEmployeeAsync(employeeId);
                if (existingShift != null)
                {
                    MessageBox.Show("يوجد شيفت مفتوح بالفعل لهذا الموظف", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إنشاء شيفت جديد
                var shift = new Shift
                {
                    ShiftNumber = await GenerateShiftNumber(),
                    EmployeeId = employeeId,
                    StartTime = DateTime.Now,
                    OpeningCash = numOpeningCash.Value,
                    Status = Utils.ShiftStatus.Open
                };

                var shiftId = await _shiftRepository.AddAsync(shift);
                shift.Id = shiftId;
                
                CurrentShift = shift;
                
                MessageBox.Show($"تم فتح الشيفت بنجاح\nرقم الشيفت: {shift.ShiftNumber}", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الشيفت: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnCloseShift_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbEmployee.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار الموظف", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var employeeId = (int)cmbEmployee.SelectedValue;
                
                // البحث عن الشيفت المفتوح
                var openShift = await _shiftRepository.GetOpenShiftByEmployeeAsync(employeeId);
                if (openShift == null)
                {
                    MessageBox.Show("لا يوجد شيفت مفتوح لهذا الموظف", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إغلاق الشيفت
                await _shiftRepository.CloseShiftAsync(openShift.Id, numClosingCash.Value, txtNotes.Text.Trim());
                
                MessageBox.Show("تم إغلاق الشيفت بنجاح", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // مسح النموذج
                ClearForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إغلاق الشيفت: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnCheckShift_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbEmployee.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار الموظف", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var employeeId = (int)cmbEmployee.SelectedValue;
                var openShift = await _shiftRepository.GetOpenShiftByEmployeeAsync(employeeId);
                
                if (openShift != null)
                {
                    lblShiftInfo.Text = $"الشيفت المفتوح: {openShift.ShiftNumber}\n" +
                                       $"وقت البداية: {openShift.StartTime:yyyy-MM-dd HH:mm}\n" +
                                       $"النقد الافتتاحي: {openShift.OpeningCash:C}";
                    
                    CurrentShift = openShift;
                    btnUseExistingShift.Enabled = true;
                }
                else
                {
                    lblShiftInfo.Text = "لا يوجد شيفت مفتوح لهذا الموظف";
                    btnUseExistingShift.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من الشيفت: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnUseExistingShift_Click(object sender, EventArgs e)
        {
            if (CurrentShift != null)
            {
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void ClearForm()
        {
            numOpeningCash.Value = 0;
            numClosingCash.Value = 0;
            txtNotes.Clear();
            lblShiftInfo.Text = "معلومات الشيفت ستظهر هنا";
            btnUseExistingShift.Enabled = false;
        }

        private async System.Threading.Tasks.Task<string> GenerateShiftNumber()
        {
            var today = DateTime.Now.ToString("yyyyMMdd");
            var shiftsToday = await _shiftRepository.GetByDateAsync(DateTime.Now.Date);
            var shiftCount = shiftsToday.Count() + 1;
            
            return $"SH-{today}-{shiftCount:D3}";
        }
    }
}
