using System;
using System.Windows.Forms;
using PizzaRestoApp.Repositories;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.UI.Auth
{
    /// <summary>
    /// شاشة تسجيل الدخول
    /// Login Form
    /// </summary>
    public partial class LoginForm : Form
    {
        private readonly EmployeeRepository _employeeRepository;
        private int _loginAttempts = 0;
        private const int MAX_LOGIN_ATTEMPTS = 3;

        public LoginForm()
        {
            InitializeComponent();
            _employeeRepository = new EmployeeRepository();
            
            this.Text = "تسجيل الدخول - Pizza Restaurant Login";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            
            // إعداد القيم الافتراضية
            txtUsername.Text = "admin";
            txtPassword.Text = "admin123";
            
            // التركيز على حقل اسم المستخدم
            txtUsername.Focus();
        }

        private async void btnLogin_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المستخدم", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    MessageBox.Show("يرجى إدخال كلمة المرور", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPassword.Focus();
                    return;
                }

                btnLogin.Enabled = false;
                btnLogin.Text = "جاري التحقق...";

                // التحقق من بيانات المستخدم
                var employee = await _employeeRepository.AuthenticateAsync(txtUsername.Text.Trim(), txtPassword.Text);

                if (employee != null && employee.IsActive)
                {
                    // حفظ بيانات المستخدم الحالي
                    CurrentUser.Employee = employee;
                    CurrentUser.LoginTime = DateTime.Now;
                    
                    MessageBox.Show($"مرحباً {employee.FullName}\nتم تسجيل الدخول بنجاح", "نجح تسجيل الدخول", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    _loginAttempts++;
                    
                    if (_loginAttempts >= MAX_LOGIN_ATTEMPTS)
                    {
                        MessageBox.Show("تم تجاوز عدد محاولات تسجيل الدخول المسموحة\nسيتم إغلاق التطبيق", "خطأ", 
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        Application.Exit();
                        return;
                    }
                    
                    MessageBox.Show($"اسم المستخدم أو كلمة المرور غير صحيحة\nالمحاولات المتبقية: {MAX_LOGIN_ATTEMPTS - _loginAttempts}", 
                        "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    
                    txtPassword.Clear();
                    txtUsername.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الدخول: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnLogin.Enabled = true;
                btnLogin.Text = "تسجيل الدخول";
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void txtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnLogin_Click(sender, e);
            }
        }

        private void txtUsername_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtPassword.Focus();
            }
        }

        private void chkShowPassword_CheckedChanged(object sender, EventArgs e)
        {
            txtPassword.UseSystemPasswordChar = !chkShowPassword.Checked;
        }

        private void btnCreateAdmin_Click(object sender, EventArgs e)
        {
            try
            {
                // إنشاء حساب المدير الافتراضي
                var result = MessageBox.Show("هل تريد إنشاء حساب المدير الافتراضي؟\n\nاسم المستخدم: admin\nكلمة المرور: admin123", 
                    "إنشاء حساب المدير", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result == DialogResult.Yes)
                {
                    CreateDefaultAdmin();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء حساب المدير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void CreateDefaultAdmin()
        {
            try
            {
                // التحقق من وجود المدير مسبقاً
                var existingAdmin = await _employeeRepository.GetByUsernameAsync("admin");
                if (existingAdmin != null)
                {
                    MessageBox.Show("حساب المدير موجود بالفعل", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // إنشاء حساب المدير
                var adminEmployee = new Models.Employee
                {
                    FirstName = "مدير",
                    LastName = "النظام",
                    Username = "admin",
                    Password = PasswordHelper.HashPassword("admin123"),
                    Email = "<EMAIL>",
                    Phone = "01000000000",
                    HireDate = DateTime.Now,
                    IsActive = true,
                    RoleId = 1 // دور المدير
                };

                await _employeeRepository.AddAsync(adminEmployee);
                
                MessageBox.Show("تم إنشاء حساب المدير بنجاح\n\nاسم المستخدم: admin\nكلمة المرور: admin123", 
                    "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                txtUsername.Text = "admin";
                txtPassword.Text = "admin123";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء حساب المدير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            // عرض معلومات النظام
            lblSystemInfo.Text = $"نظام إدارة مطعم البيزيريا\nالإصدار 1.0\n{DateTime.Now:yyyy}";
        }
    }

    /// <summary>
    /// فئة لحفظ بيانات المستخدم الحالي
    /// </summary>
    public static class CurrentUser
    {
        public static Models.Employee Employee { get; set; }
        public static DateTime LoginTime { get; set; }
        
        public static bool IsLoggedIn => Employee != null;
        
        public static bool HasPermission(string permission)
        {
            // يمكن إضافة منطق الصلاحيات هنا
            return Employee?.IsActive == true;
        }
        
        public static void Logout()
        {
            Employee = null;
            LoginTime = DateTime.MinValue;
        }
    }

    /// <summary>
    /// مساعد تشفير كلمات المرور
    /// </summary>
    public static class PasswordHelper
    {
        public static string HashPassword(string password)
        {
            // تشفير بسيط - يمكن استخدام BCrypt في الإنتاج
            return Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(password + "PizzaSalt"));
        }
        
        public static bool VerifyPassword(string password, string hashedPassword)
        {
            return HashPassword(password) == hashedPassword;
        }
    }
}
