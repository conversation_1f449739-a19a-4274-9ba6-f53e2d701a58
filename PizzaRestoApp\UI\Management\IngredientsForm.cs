using System;
using System.Linq;
using System.Windows.Forms;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;

namespace PizzaRestoApp.UI.Management
{
    /// <summary>
    /// شاشة إدارة المكونات
    /// Ingredients Management Form
    /// </summary>
    public partial class IngredientsForm : Form
    {
        private readonly IngredientRepository _ingredientRepository;
        private readonly UnitRepository _unitRepository;

        public IngredientsForm()
        {
            InitializeComponent();
            _ingredientRepository = new IngredientRepository();
            _unitRepository = new UnitRepository();

            this.Text = "إدارة المكونات - Ingredients Management";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // تحميل الوحدات في ComboBox
                var units = await _unitRepository.GetAllAsync();
                cmbUnit.DataSource = units.ToList();
                cmbUnit.DisplayMember = "Name";
                cmbUnit.ValueMember = "Id";

                // تحميل المكونات في DataGridView
                await LoadIngredients();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadIngredients()
        {
            var ingredients = await _ingredientRepository.GetAllWithUnitsAsync();
            dgvIngredients.DataSource = ingredients.ToList();

            // تخصيص أعمدة DataGridView
            if (dgvIngredients.Columns.Count > 0)
            {
                dgvIngredients.Columns["Id"].HeaderText = "المعرف";
                dgvIngredients.Columns["Name"].HeaderText = "اسم المكون";
                dgvIngredients.Columns["NameFr"].HeaderText = "الاسم بالفرنسية";
                dgvIngredients.Columns["CostPerUnit"].HeaderText = "التكلفة لكل وحدة";
                dgvIngredients.Columns["CurrentStock"].HeaderText = "المخزون الحالي";
                dgvIngredients.Columns["MinStockAlert"].HeaderText = "حد التنبيه";
                dgvIngredients.Columns["IsActive"].HeaderText = "نشط";

                // إخفاء الأعمدة غير المطلوبة
                dgvIngredients.Columns["UnitId"].Visible = false;
                dgvIngredients.Columns["CreatedAt"].Visible = false;
                dgvIngredients.Columns["UpdatedAt"].Visible = false;
                dgvIngredients.Columns["Unit"].Visible = false;
            }
        }

        private void ClearForm()
        {
            txtName.Clear();
            txtNameFr.Clear();
            numCostPerUnit.Value = 0;
            numCurrentStock.Value = 0;
            numMinStockAlert.Value = 10;
            chkIsActive.Checked = true;
            cmbUnit.SelectedIndex = -1;

            btnAdd.Enabled = true;
            btnUpdate.Enabled = false;
            btnDelete.Enabled = false;
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;

                var ingredient = new Ingredient
                {
                    Name = txtName.Text.Trim(),
                    NameFr = txtNameFr.Text.Trim(),
                    UnitId = (int)cmbUnit.SelectedValue,
                    CostPerUnit = numCostPerUnit.Value,
                    CurrentStock = numCurrentStock.Value,
                    MinStockAlert = numMinStockAlert.Value,
                    IsActive = chkIsActive.Checked
                };

                await _ingredientRepository.AddAsync(ingredient);
                MessageBox.Show("تم إضافة المكون بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                ClearForm();
                await LoadIngredients();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المكون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;
                if (dgvIngredients.CurrentRow == null) return;

                var ingredientId = (int)dgvIngredients.CurrentRow.Cells["Id"].Value;
                var ingredient = await _ingredientRepository.GetByIdAsync(ingredientId);

                if (ingredient != null)
                {
                    ingredient.Name = txtName.Text.Trim();
                    ingredient.NameFr = txtNameFr.Text.Trim();
                    ingredient.UnitId = (int)cmbUnit.SelectedValue;
                    ingredient.CostPerUnit = numCostPerUnit.Value;
                    ingredient.CurrentStock = numCurrentStock.Value;
                    ingredient.MinStockAlert = numMinStockAlert.Value;
                    ingredient.IsActive = chkIsActive.Checked;

                    await _ingredientRepository.UpdateAsync(ingredient);
                    MessageBox.Show("تم تحديث المكون بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    ClearForm();
                    await LoadIngredients();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث المكون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvIngredients.CurrentRow == null) return;

                if (MessageBox.Show("هل أنت متأكد من حذف هذا المكون؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    var ingredientId = (int)dgvIngredients.CurrentRow.Cells["Id"].Value;
                    await _ingredientRepository.DeleteAsync(ingredientId);

                    MessageBox.Show("تم حذف المكون بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    ClearForm();
                    await LoadIngredients();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المكون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dgvIngredients_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvIngredients.CurrentRow != null)
            {
                var row = dgvIngredients.CurrentRow;

                txtName.Text = row.Cells["Name"].Value?.ToString() ?? "";
                txtNameFr.Text = row.Cells["NameFr"].Value?.ToString() ?? "";
                numCostPerUnit.Value = Convert.ToDecimal(row.Cells["CostPerUnit"].Value ?? 0);
                numCurrentStock.Value = Convert.ToDecimal(row.Cells["CurrentStock"].Value ?? 0);
                numMinStockAlert.Value = Convert.ToDecimal(row.Cells["MinStockAlert"].Value ?? 10);
                chkIsActive.Checked = Convert.ToBoolean(row.Cells["IsActive"].Value);
                cmbUnit.SelectedValue = Convert.ToInt32(row.Cells["UnitId"].Value);

                btnAdd.Enabled = false;
                btnUpdate.Enabled = true;
                btnDelete.Enabled = true;
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المكون", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtNameFr.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم بالفرنسية", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNameFr.Focus();
                return false;
            }

            if (cmbUnit.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار الوحدة", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbUnit.Focus();
                return false;
            }

            return true;
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private async void btnLowStock_Click(object sender, EventArgs e)
        {
            try
            {
                var lowStockIngredients = await _ingredientRepository.GetLowStockIngredientsAsync();

                if (lowStockIngredients.Any())
                {
                    var message = "المكونات منخفضة المخزون:\n\n";
                    foreach (var ingredient in lowStockIngredients)
                    {
                        message += $"• {ingredient.Name}: {ingredient.CurrentStock} {ingredient.Unit?.Abbreviation}\n";
                    }

                    MessageBox.Show(message, "تنبيه المخزون",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                else
                {
                    MessageBox.Show("جميع المكونات في مستوى آمن", "حالة المخزون",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فحص المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
