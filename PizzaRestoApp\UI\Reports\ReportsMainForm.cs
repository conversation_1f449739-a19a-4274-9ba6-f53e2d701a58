using System;
using System.Windows.Forms;

namespace PizzaRestoApp.UI.Reports
{
    /// <summary>
    /// شاشة التقارير الرئيسية
    /// Reports Main Form
    /// </summary>
    public partial class ReportsMainForm : Form
    {
        public ReportsMainForm()
        {
            InitializeComponent();
            this.Text = "التقارير - Reports";
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void btnSalesReports_Click(object sender, EventArgs e)
        {
            try
            {
                var salesReportsForm = new SalesReportsForm();
                salesReportsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقارير المبيعات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnInventoryReports_Click(object sender, EventArgs e)
        {
            try
            {
                var inventoryReportsForm = new InventoryReportsForm();
                inventoryReportsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقارير المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnEmployeeReports_Click(object sender, EventArgs e)
        {
            try
            {
                var employeeReportsForm = new EmployeeReportsForm();
                employeeReportsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقارير الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnShiftReports_Click(object sender, EventArgs e)
        {
            try
            {
                var shiftReportsForm = new ShiftReportsForm();
                shiftReportsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقارير الشيفتات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCommissionReports_Click(object sender, EventArgs e)
        {
            try
            {
                var commissionReportsForm = new CommissionReportsForm();
                commissionReportsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقارير العمولات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDishReports_Click(object sender, EventArgs e)
        {
            try
            {
                var dishReportsForm = new DishReportsForm();
                dishReportsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقارير الأطباق: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnFinancialSummary_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("الملخص المالي:\n\nمبيعات اليوم: 0.00 ج.م\nمصروفات اليوم: 0.00 ج.م\nصافي الربح: 0.00 ج.م",
                    "الملخص المالي", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الملخص المالي: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDashboard_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("لوحة المعلومات:\n\nإجمالي الطلبات: 0\nالموظفون النشطون: 0\nمكونات منخفضة المخزون: 0\nإيرادات اليوم: 0.00 ج.م",
                    "لوحة المعلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض لوحة المعلومات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
