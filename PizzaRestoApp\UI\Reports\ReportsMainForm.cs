using System;
using System.Windows.Forms;

namespace PizzaRestoApp.UI.Reports
{
    /// <summary>
    /// شاشة التقارير الرئيسية
    /// Reports Main Form
    /// </summary>
    public partial class ReportsMainForm : Form
    {
        public ReportsMainForm()
        {
            InitializeComponent();
            this.Text = "التقارير - Reports";
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void btnSalesReports_Click(object sender, EventArgs e)
        {
            var salesReportsForm = new SalesReportsForm();
            salesReportsForm.ShowDialog();
        }

        private void btnInventoryReports_Click(object sender, EventArgs e)
        {
            var inventoryReportsForm = new InventoryReportsForm();
            inventoryReportsForm.ShowDialog();
        }

        private void btnEmployeeReports_Click(object sender, EventArgs e)
        {
            var employeeReportsForm = new EmployeeReportsForm();
            employeeReportsForm.ShowDialog();
        }

        private void btnShiftReports_Click(object sender, EventArgs e)
        {
            var shiftReportsForm = new ShiftReportsForm();
            shiftReportsForm.ShowDialog();
        }

        private void btnCommissionReports_Click(object sender, EventArgs e)
        {
            var commissionReportsForm = new CommissionReportsForm();
            commissionReportsForm.ShowDialog();
        }

        private void btnDishReports_Click(object sender, EventArgs e)
        {
            var dishReportsForm = new DishReportsForm();
            dishReportsForm.ShowDialog();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
