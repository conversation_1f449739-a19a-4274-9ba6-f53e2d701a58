using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.UI.Management
{
    /// <summary>
    /// شاشة إدارة المشتريات
    /// Purchases Management Form
    /// </summary>
    public partial class PurchasesForm : Form
    {
        private readonly PurchaseRepository _purchaseRepository;
        private readonly PurchaseDetailRepository _purchaseDetailRepository;
        private readonly SupplierRepository _supplierRepository;
        private readonly EmployeeRepository _employeeRepository;
        private readonly IngredientRepository _ingredientRepository;
        private readonly StockMovementRepository _stockMovementRepository;

        private List<PurchaseDetail> _currentPurchaseDetails;

        public PurchasesForm()
        {
            InitializeComponent();
            _purchaseRepository = new PurchaseRepository();
            _purchaseDetailRepository = new PurchaseDetailRepository();
            _supplierRepository = new SupplierRepository();
            _employeeRepository = new EmployeeRepository();
            _ingredientRepository = new IngredientRepository();
            _stockMovementRepository = new StockMovementRepository();

            _currentPurchaseDetails = new List<PurchaseDetail>();

            this.Text = "إدارة المشتريات - Purchases Management";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // تحميل الموردين
                var suppliers = await _supplierRepository.GetActiveSuppliersAsync();
                cmbSupplier.DataSource = suppliers.ToList();
                cmbSupplier.DisplayMember = "Name";
                cmbSupplier.ValueMember = "Id";

                // تحميل الموظفين
                var employees = await _employeeRepository.GetActiveEmployeesAsync();
                cmbEmployee.DataSource = employees.ToList();
                cmbEmployee.DisplayMember = "FullName";
                cmbEmployee.ValueMember = "Id";

                // تحميل المكونات
                var ingredients = await _ingredientRepository.GetActiveIngredientsAsync();
                cmbIngredient.DataSource = ingredients.ToList();
                cmbIngredient.DisplayMember = "Name";
                cmbIngredient.ValueMember = "Id";

                // إعداد حالات المشتريات
                cmbStatus.Items.Add(new { Text = "معلقة", Value = PurchaseStatus.Pending });
                cmbStatus.Items.Add(new { Text = "مؤكدة", Value = PurchaseStatus.Confirmed });
                cmbStatus.Items.Add(new { Text = "مستلمة", Value = PurchaseStatus.Received });
                cmbStatus.Items.Add(new { Text = "ملغية", Value = PurchaseStatus.Cancelled });
                cmbStatus.DisplayMember = "Text";
                cmbStatus.ValueMember = "Value";
                cmbStatus.SelectedIndex = 0;

                // تحميل المشتريات
                await LoadPurchases();

                // إعداد DataGridView للتفاصيل
                SetupPurchaseDetailsGrid();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadPurchases()
        {
            var purchases = await _purchaseRepository.GetAllWithDetailsAsync();
            dgvPurchases.DataSource = purchases.ToList();

            // تخصيص أعمدة DataGridView
            if (dgvPurchases.Columns.Count > 0)
            {
                dgvPurchases.Columns["Id"].HeaderText = "المعرف";
                dgvPurchases.Columns["PurchaseNumber"].HeaderText = "رقم المشتريات";
                dgvPurchases.Columns["PurchaseDate"].HeaderText = "تاريخ المشتريات";
                dgvPurchases.Columns["TotalAmount"].HeaderText = "المبلغ الإجمالي";
                dgvPurchases.Columns["Status"].HeaderText = "الحالة";
                dgvPurchases.Columns["Notes"].HeaderText = "ملاحظات";

                // إخفاء الأعمدة غير المطلوبة
                dgvPurchases.Columns["SupplierId"].Visible = false;
                dgvPurchases.Columns["EmployeeId"].Visible = false;
                dgvPurchases.Columns["CreatedAt"].Visible = false;
                dgvPurchases.Columns["UpdatedAt"].Visible = false;
                dgvPurchases.Columns["Supplier"].Visible = false;
                dgvPurchases.Columns["Employee"].Visible = false;
            }
        }

        private void SetupPurchaseDetailsGrid()
        {
            dgvPurchaseDetails.AutoGenerateColumns = false;
            dgvPurchaseDetails.Columns.Clear();

            dgvPurchaseDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "IngredientName",
                HeaderText = "المكون",
                DataPropertyName = "IngredientName",
                Width = 200
            });

            dgvPurchaseDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Quantity",
                HeaderText = "الكمية",
                DataPropertyName = "Quantity",
                Width = 100
            });

            dgvPurchaseDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UnitCost",
                HeaderText = "التكلفة للوحدة",
                DataPropertyName = "UnitCost",
                Width = 120
            });

            dgvPurchaseDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalCost",
                HeaderText = "التكلفة الإجمالية",
                DataPropertyName = "TotalCost",
                Width = 120
            });

            var deleteButtonColumn = new DataGridViewButtonColumn
            {
                Name = "Delete",
                HeaderText = "حذف",
                Text = "حذف",
                UseColumnTextForButtonValue = true,
                Width = 60
            };
            dgvPurchaseDetails.Columns.Add(deleteButtonColumn);
        }

        private void ClearForm()
        {
            txtPurchaseNumber.Clear();
            dtpPurchaseDate.Value = DateTime.Now;
            cmbSupplier.SelectedIndex = -1;
            cmbEmployee.SelectedIndex = -1;
            cmbStatus.SelectedIndex = 0;
            txtNotes.Clear();
            lblTotalAmount.Text = "المبلغ الإجمالي: 0.00";

            _currentPurchaseDetails.Clear();
            RefreshPurchaseDetailsGrid();

            btnAdd.Enabled = true;
            btnUpdate.Enabled = false;
            btnDelete.Enabled = false;
            btnReceive.Enabled = false;
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;
                if (!_currentPurchaseDetails.Any())
                {
                    MessageBox.Show("يرجى إضافة مكونات للمشتريات", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var purchase = new Purchase
                {
                    PurchaseNumber = txtPurchaseNumber.Text.Trim(),
                    SupplierId = (int)cmbSupplier.SelectedValue,
                    EmployeeId = (int)cmbEmployee.SelectedValue,
                    PurchaseDate = dtpPurchaseDate.Value,
                    TotalAmount = _currentPurchaseDetails.Sum(pd => pd.TotalCost),
                    Status = (PurchaseStatus)((dynamic)cmbStatus.SelectedItem).Value,
                    Notes = txtNotes.Text.Trim()
                };

                var purchaseId = await _purchaseRepository.AddAsync(purchase);

                // إضافة تفاصيل المشتريات
                foreach (var detail in _currentPurchaseDetails)
                {
                    detail.PurchaseId = purchaseId;
                    await _purchaseDetailRepository.AddAsync(detail);
                }

                MessageBox.Show("تم إضافة المشتريات بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                ClearForm();
                await LoadPurchases();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المشتريات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnAddIngredient_Click(object sender, EventArgs e)
        {
            try
            {
                if (cmbIngredient.SelectedValue == null || numQuantity.Value <= 0 || numUnitCost.Value <= 0)
                {
                    MessageBox.Show("يرجى اختيار المكون وإدخال الكمية والتكلفة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var ingredientId = (int)cmbIngredient.SelectedValue;
                var ingredient = await _ingredientRepository.GetByIdAsync(ingredientId);

                // التحقق من عدم وجود المكون مسبقاً
                if (_currentPurchaseDetails.Any(pd => pd.IngredientId == ingredientId))
                {
                    MessageBox.Show("هذا المكون موجود بالفعل في المشتريات", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var purchaseDetail = new PurchaseDetail
                {
                    IngredientId = ingredientId,
                    Ingredient = ingredient,
                    Quantity = numQuantity.Value,
                    UnitCost = numUnitCost.Value
                };
                purchaseDetail.UpdateTotalCost();

                _currentPurchaseDetails.Add(purchaseDetail);
                RefreshPurchaseDetailsGrid();
                UpdateTotalAmount();

                // مسح الحقول
                cmbIngredient.SelectedIndex = -1;
                numQuantity.Value = 0;
                numUnitCost.Value = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المكون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshPurchaseDetailsGrid()
        {
            var displayData = _currentPurchaseDetails.Select(pd => new
            {
                IngredientName = pd.Ingredient?.Name ?? "غير محدد",
                Quantity = pd.Quantity,
                UnitCost = pd.UnitCost,
                TotalCost = pd.TotalCost
            }).ToList();

            dgvPurchaseDetails.DataSource = displayData;
        }

        private void UpdateTotalAmount()
        {
            var total = _currentPurchaseDetails.Sum(pd => pd.TotalCost);
            lblTotalAmount.Text = $"المبلغ الإجمالي: {total:C}";
        }

        private void dgvPurchaseDetails_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == dgvPurchaseDetails.Columns["Delete"].Index && e.RowIndex >= 0)
            {
                _currentPurchaseDetails.RemoveAt(e.RowIndex);
                RefreshPurchaseDetailsGrid();
                UpdateTotalAmount();
            }
        }

        private async void dgvPurchases_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvPurchases.CurrentRow != null)
            {
                var row = dgvPurchases.CurrentRow;
                var purchaseId = (int)row.Cells["Id"].Value;

                txtPurchaseNumber.Text = row.Cells["PurchaseNumber"].Value?.ToString() ?? "";
                dtpPurchaseDate.Value = Convert.ToDateTime(row.Cells["PurchaseDate"].Value);
                cmbSupplier.SelectedValue = Convert.ToInt32(row.Cells["SupplierId"].Value);
                cmbEmployee.SelectedValue = Convert.ToInt32(row.Cells["EmployeeId"].Value);
                txtNotes.Text = row.Cells["Notes"].Value?.ToString() ?? "";

                var status = (PurchaseStatus)Enum.Parse(typeof(PurchaseStatus), row.Cells["Status"].Value.ToString());
                cmbStatus.SelectedValue = status;

                // تحميل تفاصيل المشتريات
                await LoadPurchaseDetails(purchaseId);

                btnAdd.Enabled = false;
                btnUpdate.Enabled = true;
                btnDelete.Enabled = true;
                btnReceive.Enabled = status == PurchaseStatus.Confirmed;
            }
        }

        private async System.Threading.Tasks.Task LoadPurchaseDetails(int purchaseId)
        {
            var details = await _purchaseRepository.GetPurchaseDetailsAsync(purchaseId);
            _currentPurchaseDetails = details.ToList();
            RefreshPurchaseDetailsGrid();
            UpdateTotalAmount();
        }

        private async void btnReceive_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvPurchases.CurrentRow == null) return;

                var purchaseId = (int)dgvPurchases.CurrentRow.Cells["Id"].Value;
                var employeeId = (int)cmbEmployee.SelectedValue;

                if (MessageBox.Show("هل تريد تأكيد استلام هذه المشتريات؟\nسيتم تحديث المخزون تلقائياً.",
                    "تأكيد الاستلام", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    // تحديث حالة المشتريات
                    await _purchaseRepository.UpdateStatusAsync(purchaseId, PurchaseStatus.Received);

                    // تحديث المخزون
                    foreach (var detail in _currentPurchaseDetails)
                    {
                        // تحديث مخزون المكون
                        var ingredient = await _ingredientRepository.GetByIdAsync(detail.IngredientId);
                        if (ingredient != null)
                        {
                            ingredient.CurrentStock += detail.Quantity;
                            ingredient.CostPerUnit = detail.UnitCost; // تحديث التكلفة
                            await _ingredientRepository.UpdateAsync(ingredient);
                        }

                        // إضافة حركة مخزون
                        await _stockMovementRepository.AddPurchaseMovementAsync(
                            detail.IngredientId, detail.Quantity, purchaseId, employeeId);
                    }

                    MessageBox.Show("تم استلام المشتريات وتحديث المخزون بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    await LoadPurchases();
                    ClearForm();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استلام المشتريات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnGenerateNumber_Click(object sender, EventArgs e)
        {
            try
            {
                var purchaseNumber = await _purchaseRepository.GetNextPurchaseNumberAsync();
                txtPurchaseNumber.Text = purchaseNumber;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء رقم المشتريات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtPurchaseNumber.Text))
            {
                MessageBox.Show("يرجى إدخال رقم المشتريات", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPurchaseNumber.Focus();
                return false;
            }

            if (cmbSupplier.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار المورد", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbSupplier.Focus();
                return false;
            }

            if (cmbEmployee.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار الموظف", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbEmployee.Focus();
                return false;
            }

            return true;
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
