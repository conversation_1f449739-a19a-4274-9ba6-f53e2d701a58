using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PizzaRestoApp.Models;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع حركة المخزون
    /// Stock Movement Repository
    /// </summary>
    public class StockMovementRepository : BaseRepository<StockMovement>
    {
        public StockMovementRepository() : base("stock_movements") { }

        /// <summary>
        /// إضافة حركة مخزون جديدة
        /// Add new stock movement
        /// </summary>
        public override async Task<int> AddAsync(StockMovement stockMovement)
        {
            var sql = @"
                INSERT INTO stock_movements (ingredient_id, movement_type, quantity, reference_type, 
                                           reference_id, notes, movement_date, employee_id, created_at)
                VALUES (@IngredientId, @MovementType, @Quantity, @ReferenceType, 
                        @ReferenceId, @Notes, @MovementDate, @EmployeeId, @CreatedAt);
                SELECT LAST_INSERT_ID();";

            stockMovement.CreatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, stockMovement);
        }

        /// <summary>
        /// تحديث حركة مخزون موجودة
        /// Update existing stock movement
        /// </summary>
        public override async Task<bool> UpdateAsync(StockMovement stockMovement)
        {
            var sql = @"
                UPDATE stock_movements 
                SET ingredient_id = @IngredientId, movement_type = @MovementType, 
                    quantity = @Quantity, reference_type = @ReferenceType,
                    reference_id = @ReferenceId, notes = @Notes, 
                    movement_date = @MovementDate, employee_id = @EmployeeId
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, stockMovement);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على حركة المخزون مع تفاصيل المكون والموظف
        /// Get stock movement with ingredient and employee details
        /// </summary>
        public async Task<StockMovement?> GetByIdWithDetailsAsync(int id)
        {
            var sql = @"
                SELECT sm.*, 
                       i.name as IngredientName, i.name_fr as IngredientNameFr,
                       u.name as UnitName, u.abbreviation as UnitAbbreviation,
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM stock_movements sm
                INNER JOIN ingredients i ON sm.ingredient_id = i.id
                INNER JOIN units u ON i.unit_id = u.id
                INNER JOIN employees e ON sm.employee_id = e.id
                WHERE sm.id = @Id";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<StockMovement, Ingredient, Unit, Employee, StockMovement>(
                sql,
                (stockMovement, ingredient, unit, employee) =>
                {
                    ingredient.Unit = unit;
                    stockMovement.Ingredient = ingredient;
                    stockMovement.Employee = employee;
                    return stockMovement;
                },
                new { Id = id },
                splitOn: "IngredientName,UnitName,EmployeeFirstName"
            );

            return result.FirstOrDefault();
        }

        /// <summary>
        /// الحصول على حركات المخزون حسب المكون
        /// Get stock movements by ingredient
        /// </summary>
        public async Task<IEnumerable<StockMovement>> GetByIngredientAsync(int ingredientId)
        {
            var sql = @"
                SELECT sm.*, 
                       i.name as IngredientName, i.name_fr as IngredientNameFr,
                       u.name as UnitName, u.abbreviation as UnitAbbreviation,
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM stock_movements sm
                INNER JOIN ingredients i ON sm.ingredient_id = i.id
                INNER JOIN units u ON i.unit_id = u.id
                INNER JOIN employees e ON sm.employee_id = e.id
                WHERE sm.ingredient_id = @IngredientId
                ORDER BY sm.movement_date DESC";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<StockMovement, Ingredient, Unit, Employee, StockMovement>(
                sql,
                (stockMovement, ingredient, unit, employee) =>
                {
                    ingredient.Unit = unit;
                    stockMovement.Ingredient = ingredient;
                    stockMovement.Employee = employee;
                    return stockMovement;
                },
                new { IngredientId = ingredientId },
                splitOn: "IngredientName,UnitName,EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على حركات المخزون حسب التاريخ
        /// Get stock movements by date range
        /// </summary>
        public async Task<IEnumerable<StockMovement>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            var sql = @"
                SELECT sm.*, 
                       i.name as IngredientName, i.name_fr as IngredientNameFr,
                       u.name as UnitName, u.abbreviation as UnitAbbreviation,
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM stock_movements sm
                INNER JOIN ingredients i ON sm.ingredient_id = i.id
                INNER JOIN units u ON i.unit_id = u.id
                INNER JOIN employees e ON sm.employee_id = e.id
                WHERE DATE(sm.movement_date) BETWEEN @StartDate AND @EndDate
                ORDER BY sm.movement_date DESC";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<StockMovement, Ingredient, Unit, Employee, StockMovement>(
                sql,
                (stockMovement, ingredient, unit, employee) =>
                {
                    ingredient.Unit = unit;
                    stockMovement.Ingredient = ingredient;
                    stockMovement.Employee = employee;
                    return stockMovement;
                },
                new { StartDate = startDate.Date, EndDate = endDate.Date },
                splitOn: "IngredientName,UnitName,EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على حركات المخزون حسب النوع
        /// Get stock movements by type
        /// </summary>
        public async Task<IEnumerable<StockMovement>> GetByMovementTypeAsync(StockMovementType movementType)
        {
            var sql = @"
                SELECT sm.*, 
                       i.name as IngredientName, i.name_fr as IngredientNameFr,
                       u.name as UnitName, u.abbreviation as UnitAbbreviation,
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM stock_movements sm
                INNER JOIN ingredients i ON sm.ingredient_id = i.id
                INNER JOIN units u ON i.unit_id = u.id
                INNER JOIN employees e ON sm.employee_id = e.id
                WHERE sm.movement_type = @MovementType
                ORDER BY sm.movement_date DESC";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<StockMovement, Ingredient, Unit, Employee, StockMovement>(
                sql,
                (stockMovement, ingredient, unit, employee) =>
                {
                    ingredient.Unit = unit;
                    stockMovement.Ingredient = ingredient;
                    stockMovement.Employee = employee;
                    return stockMovement;
                },
                new { MovementType = movementType },
                splitOn: "IngredientName,UnitName,EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// إضافة حركة مخزون للمشتريات
        /// Add stock movement for purchase
        /// </summary>
        public async Task<bool> AddPurchaseMovementAsync(int ingredientId, decimal quantity, int purchaseId, int employeeId)
        {
            var stockMovement = new StockMovement
            {
                IngredientId = ingredientId,
                MovementType = StockMovementType.In,
                Quantity = quantity,
                ReferenceType = StockReferenceType.Purchase,
                ReferenceId = purchaseId,
                MovementDate = DateTime.Now,
                EmployeeId = employeeId,
                Notes = "إدخال من المشتريات"
            };

            var result = await AddAsync(stockMovement);
            return result > 0;
        }

        /// <summary>
        /// إضافة حركة مخزون للطلبات
        /// Add stock movement for order
        /// </summary>
        public async Task<bool> AddOrderMovementAsync(int ingredientId, decimal quantity, int orderId, int employeeId)
        {
            var stockMovement = new StockMovement
            {
                IngredientId = ingredientId,
                MovementType = StockMovementType.Out,
                Quantity = quantity,
                ReferenceType = StockReferenceType.Order,
                ReferenceId = orderId,
                MovementDate = DateTime.Now,
                EmployeeId = employeeId,
                Notes = "خصم للطلب"
            };

            var result = await AddAsync(stockMovement);
            return result > 0;
        }
    }
}
