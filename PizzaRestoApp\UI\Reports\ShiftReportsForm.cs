using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.UI.Reports
{
    /// <summary>
    /// شاشة تقارير الشيفتات
    /// Shift Reports Form
    /// </summary>
    public partial class ShiftReportsForm : Form
    {
        private readonly ShiftRepository _shiftRepository;
        private readonly EmployeeRepository _employeeRepository;
        private readonly OrderRepository _orderRepository;

        public ShiftReportsForm()
        {
            InitializeComponent();
            _shiftRepository = new ShiftRepository();
            _employeeRepository = new EmployeeRepository();
            _orderRepository = new OrderRepository();

            this.Text = "تقارير الشيفتات - Shift Reports";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            InitializeReports();
        }

        private async void InitializeReports()
        {
            // إعداد التواريخ الافتراضية
            dtpFromDate.Value = DateTime.Now.Date.AddDays(-7);
            dtpToDate.Value = DateTime.Now.Date;

            // تحميل الموظفين
            var employees = await _employeeRepository.GetActiveEmployeesAsync();
            cmbEmployee.DataSource = employees.ToList();
            cmbEmployee.DisplayMember = "FullName";
            cmbEmployee.ValueMember = "Id";
            cmbEmployee.SelectedIndex = -1;

            // إعداد أنواع التقارير
            cmbReportType.Items.Add(new { Text = "تقرير الشيفتات اليومية", Value = "Daily" });
            cmbReportType.Items.Add(new { Text = "تقرير الشيفتات المالية", Value = "Financial" });
            cmbReportType.Items.Add(new { Text = "تقرير المصروفات", Value = "Expenses" });
            cmbReportType.Items.Add(new { Text = "تقرير شامل", Value = "Comprehensive" });
            cmbReportType.DisplayMember = "Text";
            cmbReportType.ValueMember = "Value";
            cmbReportType.SelectedIndex = 0;

            // إعداد Chart
            SetupChart();

            // تحميل التقرير الافتراضي
            LoadDailyShiftReport();
        }

        private void SetupChart()
        {
            chartShift.Series.Clear();
            chartShift.ChartAreas.Clear();

            var chartArea = new ChartArea("ShiftArea");
            chartArea.AxisX.Title = "الشيفتات";
            chartArea.AxisY.Title = "المبالغ";
            chartArea.AxisX.LabelStyle.Angle = -45;
            chartShift.ChartAreas.Add(chartArea);

            var series = new Series("المبيعات");
            series.ChartType = SeriesChartType.Column;
            series.Color = Color.SteelBlue;
            chartShift.Series.Add(series);
        }

        private async void LoadDailyShiftReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var shifts = await _shiftRepository.GetShiftsByDateRangeAsync(fromDate, toDate);

                if (cmbEmployee.SelectedValue != null)
                {
                    var employeeId = (int)cmbEmployee.SelectedValue;
                    shifts = shifts.Where(s => s.EmployeeId == employeeId);
                }

                // إحصائيات الشيفتات
                var totalShifts = shifts.Count();
                var openShifts = shifts.Where(s => s.EndTime == null).Count();
                var closedShifts = shifts.Where(s => s.EndTime != null).Count();
                var totalSales = shifts.Sum(s => s.TotalSales);
                var totalCash = shifts.Sum(s => s.CashAmount);
                var totalExpenses = shifts.Sum(s => s.TotalExpenses);

                lblTotalShifts.Text = $"إجمالي الشيفتات: {totalShifts}";
                lblOpenShifts.Text = $"شيفتات مفتوحة: {openShifts}";
                lblClosedShifts.Text = $"شيفتات مغلقة: {closedShifts}";
                lblTotalSales.Text = $"إجمالي المبيعات: {totalSales:C}";
                lblTotalCash.Text = $"إجمالي النقد: {totalCash:C}";
                lblTotalExpenses.Text = $"إجمالي المصروفات: {totalExpenses:C}";

                // تحميل تفاصيل الشيفتات
                var shiftDetails = shifts.Select(s => new
                {
                    EmployeeName = s.Employee?.FullName ?? "غير محدد",
                    StartTime = s.StartTime.ToString("yyyy-MM-dd HH:mm"),
                    EndTime = s.EndTime?.ToString("yyyy-MM-dd HH:mm") ?? "مفتوحة",
                    Duration = s.EndTime.HasValue ?
                        $"{(s.EndTime.Value - s.StartTime).TotalHours:F1} ساعة" : "جارية",
                    OpeningCash = s.OpeningCash,
                    TotalSales = s.TotalSales,
                    CashAmount = s.CashAmount,
                    TotalExpenses = s.TotalExpenses,
                    ClosingCash = s.ClosingCash,
                    CashDifference = s.CashDifference,
                    Status = s.EndTime.HasValue ? "مغلقة" : "مفتوحة"
                }).ToList();

                dgvShiftReport.DataSource = shiftDetails;

                // تحديث الرسم البياني
                UpdateShiftChart(shifts);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير الشيفتات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadFinancialShiftReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var shifts = await _shiftRepository.GetShiftsByDateRangeAsync(fromDate, toDate);
                var closedShifts = shifts.Where(s => s.EndTime != null);

                if (cmbEmployee.SelectedValue != null)
                {
                    var employeeId = (int)cmbEmployee.SelectedValue;
                    closedShifts = closedShifts.Where(s => s.EmployeeId == employeeId);
                }

                // تحليل مالي للشيفتات
                var financialData = closedShifts.Select(s => new
                {
                    EmployeeName = s.Employee?.FullName ?? "غير محدد",
                    ShiftDate = s.StartTime.ToString("yyyy-MM-dd"),
                    OpeningCash = s.OpeningCash,
                    TotalSales = s.TotalSales,
                    CashSales = s.CashAmount,
                    CardSales = s.TotalSales - s.CashAmount, // افتراض أن الباقي بطاقات
                    TotalExpenses = s.TotalExpenses,
                    ExpectedCash = s.OpeningCash + s.CashAmount - s.TotalExpenses,
                    ActualCash = s.ClosingCash,
                    CashDifference = s.CashDifference,
                    NetProfit = s.TotalSales - s.TotalExpenses,
                    Status = s.CashDifference == 0 ? "متوازن" :
                             s.CashDifference > 0 ? "زيادة" : "نقص"
                }).ToList();

                dgvShiftReport.DataSource = financialData;

                // إحصائيات مالية
                var totalNetProfit = financialData.Sum(f => f.NetProfit);
                var totalCashDifference = financialData.Sum(f => f.CashDifference);
                var balancedShifts = financialData.Count(f => f.CashDifference == 0);
                var avgNetProfit = financialData.Count > 0 ? totalNetProfit / financialData.Count : 0;

                lblNetProfit.Text = $"صافي الربح: {totalNetProfit:C}";
                lblCashDifference.Text = $"فرق النقد: {totalCashDifference:C}";
                lblBalancedShifts.Text = $"شيفتات متوازنة: {balancedShifts}";
                lblAvgNetProfit.Text = $"متوسط الربح: {avgNetProfit:C}";

                // تحديث الرسم البياني
                UpdateFinancialChart(financialData);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التقرير المالي: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadExpensesReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var shifts = await _shiftRepository.GetShiftsByDateRangeAsync(fromDate, toDate);
                var shiftExpenses = await _shiftRepository.GetShiftExpensesByDateRangeAsync(fromDate, toDate);

                if (cmbEmployee.SelectedValue != null)
                {
                    var employeeId = (int)cmbEmployee.SelectedValue;
                    shifts = shifts.Where(s => s.EmployeeId == employeeId);
                    shiftExpenses = shiftExpenses.Where(e => shifts.Any(s => s.Id == e.ShiftId));
                }

                // تحليل المصروفات
                var expensesByCategory = shiftExpenses
                    .GroupBy(e => e.Category ?? "غير محدد")
                    .Select(g => new
                    {
                        Category = g.Key,
                        TotalAmount = g.Sum(e => e.Amount),
                        Count = g.Count(),
                        AverageAmount = g.Average(e => e.Amount)
                    })
                    .OrderByDescending(x => x.TotalAmount)
                    .ToList();

                dgvShiftReport.DataSource = expensesByCategory;

                // إحصائيات المصروفات
                var totalExpenseAmount = expensesByCategory.Sum(e => e.TotalAmount);
                var totalExpenseCount = expensesByCategory.Sum(e => e.Count);
                var avgExpensePerShift = shifts.Count() > 0 ? totalExpenseAmount / shifts.Count() : 0;
                var topExpenseCategory = expensesByCategory.FirstOrDefault()?.Category ?? "لا يوجد";

                lblTotalExpenseAmount.Text = $"إجمالي المصروفات: {totalExpenseAmount:C}";
                lblTotalExpenseCount.Text = $"عدد المصروفات: {totalExpenseCount}";
                lblAvgExpensePerShift.Text = $"متوسط المصروفات لكل شيفت: {avgExpensePerShift:C}";
                lblTopExpenseCategory.Text = $"أعلى فئة مصروفات: {topExpenseCategory}";

                // تحديث الرسم البياني
                UpdateExpensesChart(expensesByCategory);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير المصروفات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadComprehensiveShiftReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var shifts = await _shiftRepository.GetShiftsByDateRangeAsync(fromDate, toDate);
                var closedShifts = shifts.Where(s => s.EndTime != null);

                if (cmbEmployee.SelectedValue != null)
                {
                    var employeeId = (int)cmbEmployee.SelectedValue;
                    closedShifts = closedShifts.Where(s => s.EmployeeId == employeeId);
                }

                // تقرير شامل للشيفتات
                var comprehensiveData = new List<dynamic>();

                foreach (var shift in closedShifts)
                {
                    // الحصول على طلبات الشيفت
                    var shiftOrders = await _orderRepository.GetOrdersByShiftAsync(shift.Id);
                    var completedOrders = shiftOrders.Where(o => o.Status == OrderStatus.Completed);

                    var orderCount = completedOrders.Count();
                    var avgOrderValue = orderCount > 0 ? completedOrders.Sum(o => o.TotalAmount) / orderCount : 0;
                    var shiftDuration = shift.EndTime.HasValue ?
                        (shift.EndTime.Value - shift.StartTime).TotalHours : 0;

                    comprehensiveData.Add(new
                    {
                        EmployeeName = shift.Employee?.FullName ?? "غير محدد",
                        ShiftDate = shift.StartTime.ToString("yyyy-MM-dd"),
                        Duration = $"{shiftDuration:F1} ساعة",
                        OrderCount = orderCount,
                        TotalSales = shift.TotalSales,
                        AvgOrderValue = avgOrderValue,
                        TotalExpenses = shift.TotalExpenses,
                        NetProfit = shift.TotalSales - shift.TotalExpenses,
                        CashDifference = shift.CashDifference,
                        SalesPerHour = shiftDuration > 0 ? shift.TotalSales / (decimal)shiftDuration : 0,
                        OrdersPerHour = shiftDuration > 0 ? orderCount / shiftDuration : 0,
                        Efficiency = shiftDuration > 0 ? (shift.TotalSales - shift.TotalExpenses) / (decimal)shiftDuration : 0
                    });
                }

                dgvShiftReport.DataSource = comprehensiveData;

                // إحصائيات شاملة
                var totalShiftCount = comprehensiveData.Count;
                var avgSalesPerHour = comprehensiveData.Count > 0 ? comprehensiveData.Average(c => (decimal)c.SalesPerHour) : 0;
                var avgOrdersPerHour = comprehensiveData.Count > 0 ? comprehensiveData.Average(c => (double)c.OrdersPerHour) : 0;
                var avgEfficiency = comprehensiveData.Count > 0 ? comprehensiveData.Average(c => (decimal)c.Efficiency) : 0;

                lblTotalShiftCount.Text = $"إجمالي الشيفتات: {totalShiftCount}";
                lblAvgSalesPerHour.Text = $"متوسط المبيعات/ساعة: {avgSalesPerHour:C}";
                lblAvgOrdersPerHour.Text = $"متوسط الطلبات/ساعة: {avgOrdersPerHour:F1}";
                lblAvgEfficiency.Text = $"متوسط الكفاءة: {avgEfficiency:C}/ساعة";

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التقرير الشامل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateShiftChart(IEnumerable<Shift> shifts)
        {
            chartShift.Series["المبيعات"].Points.Clear();

            var shiftsByDate = shifts
                .GroupBy(s => s.StartTime.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    TotalSales = g.Sum(s => s.TotalSales),
                    ShiftCount = g.Count()
                })
                .OrderBy(x => x.Date)
                .ToList();

            foreach (var item in shiftsByDate)
            {
                chartShift.Series["المبيعات"].Points.AddXY(item.Date.ToString("MM/dd"), item.TotalSales);
            }
        }

        private void UpdateFinancialChart(List<dynamic> financialData)
        {
            chartShift.Series.Clear();
            chartShift.Series.Add(new Series("المبيعات") { ChartType = SeriesChartType.Column, Color = Color.Green });
            chartShift.Series.Add(new Series("المصروفات") { ChartType = SeriesChartType.Column, Color = Color.Red });
            chartShift.Series.Add(new Series("صافي الربح") { ChartType = SeriesChartType.Line, Color = Color.Blue });

            foreach (var item in financialData.Take(10))
            {
                chartShift.Series["المبيعات"].Points.AddXY(item.ShiftDate, item.TotalSales);
                chartShift.Series["المصروفات"].Points.AddXY(item.ShiftDate, item.TotalExpenses);
                chartShift.Series["صافي الربح"].Points.AddXY(item.ShiftDate, item.NetProfit);
            }
        }

        private void UpdateExpensesChart(List<dynamic> expensesByCategory)
        {
            chartShift.Series["المبيعات"].Points.Clear();
            chartShift.Series["المبيعات"].ChartType = SeriesChartType.Pie;

            foreach (var item in expensesByCategory.Take(10))
            {
                chartShift.Series["المبيعات"].Points.AddXY(item.Category, item.TotalAmount);
            }
        }

        private async void btnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                var reportType = ((dynamic)cmbReportType.SelectedItem).Value.ToString();

                switch (reportType)
                {
                    case "Daily":
                        await LoadDailyShiftReport();
                        break;
                    case "Financial":
                        await LoadFinancialShiftReport();
                        break;
                    case "Expenses":
                        await LoadExpensesReport();
                        break;
                    case "Comprehensive":
                        await LoadComprehensiveShiftReport();
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    FileName = $"ShiftReport_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToExcel(saveDialog.FileName);
                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToExcel(string fileName)
        {
            using (var package = new OfficeOpenXml.ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("تقرير الشيفتات");

                var data = (System.Collections.IList)dgvShiftReport.DataSource;
                if (data != null && data.Count > 0)
                {
                    for (int i = 0; i < dgvShiftReport.Columns.Count; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = dgvShiftReport.Columns[i].HeaderText;
                    }

                    for (int i = 0; i < data.Count; i++)
                    {
                        var item = data[i];
                        var properties = item.GetType().GetProperties();

                        for (int j = 0; j < properties.Length; j++)
                        {
                            worksheet.Cells[i + 2, j + 1].Value = properties[j].GetValue(item);
                        }
                    }
                }

                package.SaveAs(new System.IO.FileInfo(fileName));
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("تم إرسال التقرير للطباعة", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cmbEmployee_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnGenerateReport_Click(sender, e);
        }
    }
}
