using System;

namespace PizzaRestoApp.Utils
{
    /// <summary>
    /// الثوابت المستخدمة في التطبيق
    /// Application Constants
    /// </summary>
    public static class Constants
    {
        // أدوار الموظفين - Employee Roles
        public const string ROLE_MANAGER = "manager";
        public const string ROLE_CASHIER = "cashier";
        public const string ROLE_COOK = "cook";
        public const string ROLE_WAITER = "waiter";

        // أنواع الطلبات - Order Types
        public const string ORDER_TYPE_DINE_IN = "dine_in";
        public const string ORDER_TYPE_TAKEAWAY = "takeaway";
        public const string ORDER_TYPE_DELIVERY = "delivery";

        // حالات الطلبات - Order Status
        public const string ORDER_STATUS_NEW = "new";
        public const string ORDER_STATUS_PREPARING = "preparing";
        public const string ORDER_STATUS_READY = "ready";
        public const string ORDER_STATUS_DELIVERED = "delivered";
        public const string ORDER_STATUS_CANCELLED = "cancelled";

        // طرق الدفع - Payment Methods
        public const string PAYMENT_CASH = "cash";
        public const string PAYMENT_CARD = "card";
        public const string PAYMENT_MIXED = "mixed";

        // حالات الشيفت - Shift Status
        public const string SHIFT_STATUS_OPEN = "open";
        public const string SHIFT_STATUS_CLOSED = "closed";
        public const string SHIFT_STATUS_SUSPENDED = "suspended";

        // أنواع حركة المخزون - Stock Movement Types
        public const string STOCK_MOVEMENT_IN = "in";
        public const string STOCK_MOVEMENT_OUT = "out";
        public const string STOCK_MOVEMENT_ADJUSTMENT = "adjustment";

        // مراجع حركة المخزون - Stock Movement References
        public const string STOCK_REFERENCE_PURCHASE = "purchase";
        public const string STOCK_REFERENCE_ORDER = "order";
        public const string STOCK_REFERENCE_ADJUSTMENT = "adjustment";
        public const string STOCK_REFERENCE_WASTE = "waste";

        // حالات المشتريات - Purchase Status
        public const string PURCHASE_STATUS_PENDING = "pending";
        public const string PURCHASE_STATUS_RECEIVED = "received";
        public const string PURCHASE_STATUS_CANCELLED = "cancelled";

        // إعدادات التطبيق - Application Settings
        public const int KITCHEN_REFRESH_INTERVAL = 10000; // 10 seconds
        public const decimal DEFAULT_TAX_RATE = 0.0m; // 0% tax
        public const decimal DEFAULT_PROFIT_MARGIN = 30.0m; // 30% profit margin
        public const decimal MIN_STOCK_ALERT_THRESHOLD = 5.0m; // تنبيه عند انخفاض المخزون

        // تنسيقات التاريخ والوقت - Date and Time Formats
        public const string DATE_FORMAT = "yyyy-MM-dd";
        public const string DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
        public const string TIME_FORMAT = "HH:mm";

        // رسائل النظام - System Messages
        public const string MSG_SUCCESS = "تم بنجاح";
        public const string MSG_ERROR = "حدث خطأ";
        public const string MSG_CONFIRM_DELETE = "هل أنت متأكد من الحذف؟";
        public const string MSG_INVALID_DATA = "البيانات غير صحيحة";
        public const string MSG_REQUIRED_FIELD = "هذا الحقل مطلوب";
    }

    /// <summary>
    /// تعدادات التطبيق
    /// Application Enumerations
    /// </summary>
    public enum OrderType
    {
        DineIn,
        Takeaway,
        Delivery
    }

    public enum OrderStatus
    {
        New,
        Preparing,
        Ready,
        Delivered,
        Cancelled
    }

    public enum PaymentMethod
    {
        Cash,
        Card,
        Mixed
    }

    public enum ShiftStatus
    {
        Open,
        Closed,
        Suspended
    }

    public enum StockMovementType
    {
        In,
        Out,
        Adjustment
    }

    public enum StockReferenceType
    {
        Purchase,
        Order,
        Adjustment,
        Waste
    }

    public enum PurchaseStatus
    {
        Pending,
        Received,
        Cancelled
    }

    public enum EmployeeRole
    {
        Manager,
        Cashier,
        Cook,
        Waiter
    }
}
