using System;
using System.Collections.Generic;
using System.Linq;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج الطبق
    /// Dish Model
    /// </summary>
    public class Dish
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameFr { get; set; } = string.Empty;
        public int CategoryId { get; set; }
        public string? Description { get; set; }
        public decimal BaseCost { get; set; } = 0;
        public decimal ProfitMargin { get; set; } = 30.0m;
        public decimal SellingPrice { get; set; }
        public int PreparationTime { get; set; } = 15;
        public bool IsAvailable { get; set; } = true;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public DishCategory? Category { get; set; }
        public List<DishIngredient> Ingredients { get; set; } = new List<DishIngredient>();

        /// <summary>
        /// حساب التكلفة الأساسية من المكونات
        /// Calculate base cost from ingredients
        /// </summary>
        public decimal CalculateBaseCost()
        {
            return Ingredients?.Sum(di => di.Quantity * (di.Ingredient?.CostPerUnit ?? 0)) ?? 0;
        }

        /// <summary>
        /// حساب سعر البيع بناءً على التكلفة وهامش الربح
        /// Calculate selling price based on cost and profit margin
        /// </summary>
        public decimal CalculateSellingPrice()
        {
            var cost = BaseCost > 0 ? BaseCost : CalculateBaseCost();
            return cost * (1 + ProfitMargin / 100);
        }

        /// <summary>
        /// تحديث الأسعار تلقائياً
        /// Update prices automatically
        /// </summary>
        public void UpdatePrices()
        {
            BaseCost = CalculateBaseCost();
            SellingPrice = CalculateSellingPrice();
        }

        /// <summary>
        /// التحقق من توفر جميع المكونات
        /// Check if all ingredients are available
        /// </summary>
        public bool AreIngredientsAvailable()
        {
            return Ingredients?.All(di => 
                di.Ingredient != null && 
                di.Ingredient.CurrentStock >= di.Quantity) ?? true;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   !string.IsNullOrWhiteSpace(NameFr) &&
                   CategoryId > 0 &&
                   ProfitMargin >= 0 &&
                   SellingPrice > 0 &&
                   PreparationTime > 0;
        }
    }
}
