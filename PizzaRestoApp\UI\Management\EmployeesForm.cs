using System;
using System.Linq;
using System.Windows.Forms;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;

namespace PizzaRestoApp.UI.Management
{
    /// <summary>
    /// شاشة إدارة الموظفين
    /// Employees Management Form
    /// </summary>
    public partial class EmployeesForm : Form
    {
        private readonly EmployeeRepository _employeeRepository;
        private readonly RoleRepository _roleRepository;

        public EmployeesForm()
        {
            InitializeComponent();
            _employeeRepository = new EmployeeRepository();
            _roleRepository = new RoleRepository();
            
            this.Text = "إدارة الموظفين - Employees Management";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // تحميل الأدوار في ComboBox
                var roles = await _roleRepository.GetAllAsync();
                cmbRole.DataSource = roles.ToList();
                cmbRole.DisplayMember = "Name";
                cmbRole.ValueMember = "Id";

                // تحميل الموظفين في DataGridView
                await LoadEmployees();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadEmployees()
        {
            var employees = await _employeeRepository.GetAllWithRolesAsync();
            dgvEmployees.DataSource = employees.ToList();
            
            // تخصيص أعمدة DataGridView
            if (dgvEmployees.Columns.Count > 0)
            {
                dgvEmployees.Columns["Id"].HeaderText = "المعرف";
                dgvEmployees.Columns["EmployeeCode"].HeaderText = "كود الموظف";
                dgvEmployees.Columns["FirstName"].HeaderText = "الاسم الأول";
                dgvEmployees.Columns["LastName"].HeaderText = "الاسم الأخير";
                dgvEmployees.Columns["Phone"].HeaderText = "الهاتف";
                dgvEmployees.Columns["Email"].HeaderText = "البريد الإلكتروني";
                dgvEmployees.Columns["HireDate"].HeaderText = "تاريخ التوظيف";
                dgvEmployees.Columns["Salary"].HeaderText = "الراتب";
                dgvEmployees.Columns["PercentageRate"].HeaderText = "نسبة البرسنتاج";
                dgvEmployees.Columns["IsActive"].HeaderText = "نشط";
                
                // إخفاء الأعمدة غير المطلوبة
                dgvEmployees.Columns["RoleId"].Visible = false;
                dgvEmployees.Columns["CreatedAt"].Visible = false;
                dgvEmployees.Columns["UpdatedAt"].Visible = false;
                dgvEmployees.Columns["Role"].Visible = false;
            }
        }

        private void ClearForm()
        {
            txtEmployeeCode.Clear();
            txtFirstName.Clear();
            txtLastName.Clear();
            txtPhone.Clear();
            txtEmail.Clear();
            dtpHireDate.Value = DateTime.Now;
            numSalary.Value = 0;
            numPercentageRate.Value = 0;
            chkIsActive.Checked = true;
            cmbRole.SelectedIndex = -1;
            
            btnAdd.Enabled = true;
            btnUpdate.Enabled = false;
            btnDelete.Enabled = false;
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;

                var employee = new Employee
                {
                    EmployeeCode = txtEmployeeCode.Text.Trim(),
                    FirstName = txtFirstName.Text.Trim(),
                    LastName = txtLastName.Text.Trim(),
                    Phone = txtPhone.Text.Trim(),
                    Email = txtEmail.Text.Trim(),
                    RoleId = (int)cmbRole.SelectedValue,
                    HireDate = dtpHireDate.Value,
                    Salary = numSalary.Value,
                    PercentageRate = numPercentageRate.Value,
                    IsActive = chkIsActive.Checked
                };

                await _employeeRepository.AddAsync(employee);
                MessageBox.Show("تم إضافة الموظف بنجاح", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                ClearForm();
                await LoadEmployees();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الموظف: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;
                if (dgvEmployees.CurrentRow == null) return;

                var employeeId = (int)dgvEmployees.CurrentRow.Cells["Id"].Value;
                var employee = await _employeeRepository.GetByIdAsync(employeeId);
                
                if (employee != null)
                {
                    employee.EmployeeCode = txtEmployeeCode.Text.Trim();
                    employee.FirstName = txtFirstName.Text.Trim();
                    employee.LastName = txtLastName.Text.Trim();
                    employee.Phone = txtPhone.Text.Trim();
                    employee.Email = txtEmail.Text.Trim();
                    employee.RoleId = (int)cmbRole.SelectedValue;
                    employee.HireDate = dtpHireDate.Value;
                    employee.Salary = numSalary.Value;
                    employee.PercentageRate = numPercentageRate.Value;
                    employee.IsActive = chkIsActive.Checked;

                    await _employeeRepository.UpdateAsync(employee);
                    MessageBox.Show("تم تحديث الموظف بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    ClearForm();
                    await LoadEmployees();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الموظف: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvEmployees.CurrentRow == null) return;

                if (MessageBox.Show("هل أنت متأكد من حذف هذا الموظف؟", "تأكيد الحذف", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    var employeeId = (int)dgvEmployees.CurrentRow.Cells["Id"].Value;
                    await _employeeRepository.DeleteAsync(employeeId);
                    
                    MessageBox.Show("تم حذف الموظف بنجاح", "نجح", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    ClearForm();
                    await LoadEmployees();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الموظف: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dgvEmployees_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvEmployees.CurrentRow != null)
            {
                var row = dgvEmployees.CurrentRow;
                
                txtEmployeeCode.Text = row.Cells["EmployeeCode"].Value?.ToString() ?? "";
                txtFirstName.Text = row.Cells["FirstName"].Value?.ToString() ?? "";
                txtLastName.Text = row.Cells["LastName"].Value?.ToString() ?? "";
                txtPhone.Text = row.Cells["Phone"].Value?.ToString() ?? "";
                txtEmail.Text = row.Cells["Email"].Value?.ToString() ?? "";
                dtpHireDate.Value = Convert.ToDateTime(row.Cells["HireDate"].Value);
                numSalary.Value = Convert.ToDecimal(row.Cells["Salary"].Value ?? 0);
                numPercentageRate.Value = Convert.ToDecimal(row.Cells["PercentageRate"].Value ?? 0);
                chkIsActive.Checked = Convert.ToBoolean(row.Cells["IsActive"].Value);
                cmbRole.SelectedValue = Convert.ToInt32(row.Cells["RoleId"].Value);
                
                btnAdd.Enabled = false;
                btnUpdate.Enabled = true;
                btnDelete.Enabled = true;
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtEmployeeCode.Text))
            {
                MessageBox.Show("يرجى إدخال كود الموظف", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtEmployeeCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtFirstName.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الأول", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFirstName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtLastName.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الأخير", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtLastName.Focus();
                return false;
            }

            if (cmbRole.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار الدور", "تحقق من البيانات", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbRole.Focus();
                return false;
            }

            return true;
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
