using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع الأدوار
    /// Role Repository
    /// </summary>
    public class RoleRepository : BaseRepository<Role>
    {
        public RoleRepository() : base("roles") { }

        /// <summary>
        /// إضافة دور جديد
        /// Add new role
        /// </summary>
        public override async Task<int> AddAsync(Role role)
        {
            var sql = @"
                INSERT INTO roles (name, name_fr, description, created_at)
                VALUES (@Name, @NameFr, @Description, @CreatedAt);
                SELECT LAST_INSERT_ID();";

            role.CreatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, role);
        }

        /// <summary>
        /// تحديث دور موجود
        /// Update existing role
        /// </summary>
        public override async Task<bool> UpdateAsync(Role role)
        {
            var sql = @"
                UPDATE roles 
                SET name = @Name, name_fr = @NameFr, description = @Description
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, role);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على دور بالاسم
        /// Get role by name
        /// </summary>
        public async Task<Role?> GetByNameAsync(string name)
        {
            var sql = "SELECT * FROM roles WHERE name = @Name";
            return await QueryFirstOrDefaultAsync(sql, new { Name = name });
        }

        /// <summary>
        /// الحصول على الأدوار النشطة
        /// Get active roles
        /// </summary>
        public async Task<IEnumerable<Role>> GetActiveRolesAsync()
        {
            var sql = "SELECT * FROM roles ORDER BY name";
            return await QueryAsync(sql);
        }
    }
}
