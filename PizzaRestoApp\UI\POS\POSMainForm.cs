using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.UI.POS
{
    /// <summary>
    /// شاشة نقطة البيع الرئيسية
    /// POS Main Form
    /// </summary>
    public partial class POSMainForm : Form
    {
        private readonly DishRepository _dishRepository;
        private readonly OrderRepository _orderRepository;
        private readonly ShiftRepository _shiftRepository;
        private readonly CustomerRepository _customerRepository;
        private readonly RestaurantTableRepository _tableRepository;
        
        private List<OrderDetail> _currentOrderDetails;
        private Shift _currentShift;
        private decimal _currentSubtotal;
        private decimal _currentTax;
        private decimal _currentDiscount;
        private decimal _currentTotal;

        public POSMainForm()
        {
            InitializeComponent();
            
            _dishRepository = new DishRepository();
            _orderRepository = new OrderRepository();
            _shiftRepository = new ShiftRepository();
            _customerRepository = new CustomerRepository();
            _tableRepository = new RestaurantTableRepository();
            
            _currentOrderDetails = new List<OrderDetail>();
            
            this.Text = "نقطة البيع - Point of Sale";
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            InitializeForm();
        }

        private async void InitializeForm()
        {
            try
            {
                // تحميل الأطباق المتاحة
                await LoadAvailableDishes();
                
                // تحميل الطاولات
                await LoadTables();
                
                // إعداد نوع الطلب
                cmbOrderType.Items.Add(new { Text = "في المطعم", Value = OrderType.DineIn });
                cmbOrderType.Items.Add(new { Text = "تيك أواي", Value = OrderType.Takeaway });
                cmbOrderType.Items.Add(new { Text = "توصيل", Value = OrderType.Delivery });
                cmbOrderType.DisplayMember = "Text";
                cmbOrderType.ValueMember = "Value";
                cmbOrderType.SelectedIndex = 0;
                
                // إعداد طريقة الدفع
                cmbPaymentMethod.Items.Add(new { Text = "نقداً", Value = PaymentMethod.Cash });
                cmbPaymentMethod.Items.Add(new { Text = "بطاقة", Value = PaymentMethod.Card });
                cmbPaymentMethod.Items.Add(new { Text = "مختلط", Value = PaymentMethod.Mixed });
                cmbPaymentMethod.DisplayMember = "Text";
                cmbPaymentMethod.ValueMember = "Value";
                cmbPaymentMethod.SelectedIndex = 0;
                
                // إعداد DataGridView للطلب الحالي
                SetupOrderDetailsGrid();
                
                // تحديث المجاميع
                UpdateTotals();
                
                // التحقق من وجود شيفت مفتوح
                await CheckCurrentShift();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النموذج: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadAvailableDishes()
        {
            var dishes = await _dishRepository.GetAvailableDishesAsync();
            
            // مسح الأطباق الحالية
            panelDishes.Controls.Clear();
            
            int x = 10, y = 10;
            int buttonWidth = 120, buttonHeight = 80;
            int margin = 10;
            
            foreach (var dish in dishes)
            {
                var dishButton = new Button
                {
                    Text = $"{dish.Name}\n{dish.SellingPrice:C}",
                    Size = new System.Drawing.Size(buttonWidth, buttonHeight),
                    Location = new System.Drawing.Point(x, y),
                    BackColor = System.Drawing.Color.LightBlue,
                    Font = new System.Drawing.Font("Tahoma", 9, System.Drawing.FontStyle.Bold),
                    Tag = dish,
                    TextAlign = System.Drawing.ContentAlignment.MiddleCenter
                };
                
                dishButton.Click += DishButton_Click;
                panelDishes.Controls.Add(dishButton);
                
                x += buttonWidth + margin;
                if (x + buttonWidth > panelDishes.Width)
                {
                    x = 10;
                    y += buttonHeight + margin;
                }
            }
        }

        private async System.Threading.Tasks.Task LoadTables()
        {
            var tables = await _tableRepository.GetActiveTablesAsync();
            cmbTable.DataSource = tables.ToList();
            cmbTable.DisplayMember = "TableNumber";
            cmbTable.ValueMember = "Id";
        }

        private void SetupOrderDetailsGrid()
        {
            dgvOrderDetails.AutoGenerateColumns = false;
            dgvOrderDetails.Columns.Clear();
            
            dgvOrderDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "DishName",
                HeaderText = "الطبق",
                DataPropertyName = "DishName",
                Width = 200
            });
            
            dgvOrderDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Quantity",
                HeaderText = "الكمية",
                DataPropertyName = "Quantity",
                Width = 80
            });
            
            dgvOrderDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UnitPrice",
                HeaderText = "السعر",
                DataPropertyName = "UnitPrice",
                Width = 100
            });
            
            dgvOrderDetails.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "TotalPrice",
                HeaderText = "الإجمالي",
                DataPropertyName = "TotalPrice",
                Width = 100
            });
            
            var deleteButtonColumn = new DataGridViewButtonColumn
            {
                Name = "Delete",
                HeaderText = "حذف",
                Text = "حذف",
                UseColumnTextForButtonValue = true,
                Width = 60
            };
            dgvOrderDetails.Columns.Add(deleteButtonColumn);
        }

        private void DishButton_Click(object sender, EventArgs e)
        {
            var button = sender as Button;
            var dish = button.Tag as Dish;
            
            AddDishToOrder(dish);
        }

        private void AddDishToOrder(Dish dish)
        {
            // البحث عن الطبق في الطلب الحالي
            var existingDetail = _currentOrderDetails.FirstOrDefault(od => od.DishId == dish.Id);
            
            if (existingDetail != null)
            {
                // زيادة الكمية
                existingDetail.Quantity++;
                existingDetail.UpdateTotalPrice();
            }
            else
            {
                // إضافة طبق جديد
                var orderDetail = new OrderDetail
                {
                    DishId = dish.Id,
                    Dish = dish,
                    Quantity = 1,
                    UnitPrice = dish.SellingPrice
                };
                orderDetail.UpdateTotalPrice();
                
                _currentOrderDetails.Add(orderDetail);
            }
            
            RefreshOrderDetailsGrid();
            UpdateTotals();
        }

        private void RefreshOrderDetailsGrid()
        {
            var displayData = _currentOrderDetails.Select(od => new
            {
                DishName = od.Dish?.Name ?? "غير محدد",
                Quantity = od.Quantity,
                UnitPrice = od.UnitPrice,
                TotalPrice = od.TotalPrice
            }).ToList();
            
            dgvOrderDetails.DataSource = displayData;
        }

        private void UpdateTotals()
        {
            _currentSubtotal = _currentOrderDetails.Sum(od => od.TotalPrice);
            _currentTax = _currentSubtotal * (Constants.DEFAULT_TAX_RATE / 100);
            _currentDiscount = numDiscount.Value;
            _currentTotal = _currentSubtotal + _currentTax - _currentDiscount;
            
            lblSubtotal.Text = $"المجموع الفرعي: {_currentSubtotal:C}";
            lblTax.Text = $"الضريبة: {_currentTax:C}";
            lblDiscount.Text = $"الخصم: {_currentDiscount:C}";
            lblTotal.Text = $"الإجمالي: {_currentTotal:C}";
        }

        private async System.Threading.Tasks.Task CheckCurrentShift()
        {
            // هنا يجب تحديد الموظف الحالي - للتبسيط سنستخدم موظف افتراضي
            int currentEmployeeId = 1; // يجب تحديد هذا من نظام تسجيل الدخول
            
            _currentShift = await _shiftRepository.GetOpenShiftByEmployeeAsync(currentEmployeeId);
            
            if (_currentShift == null)
            {
                MessageBox.Show("لا يوجد شيفت مفتوح. يرجى فتح شيفت أولاً.", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                
                // فتح نموذج إدارة الشيفت
                var shiftForm = new ShiftManagementForm();
                if (shiftForm.ShowDialog() == DialogResult.OK)
                {
                    _currentShift = shiftForm.CurrentShift;
                }
                else
                {
                    this.Close();
                    return;
                }
            }
            
            lblCurrentShift.Text = $"الشيفت الحالي: {_currentShift.ShiftNumber}";
        }

        private async void btnProcessOrder_Click(object sender, EventArgs e)
        {
            try
            {
                if (!_currentOrderDetails.Any())
                {
                    MessageBox.Show("يرجى إضافة أطباق للطلب", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                if (_currentShift == null)
                {
                    MessageBox.Show("لا يوجد شيفت مفتوح", "خطأ", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                
                // إنشاء الطلب
                var order = new Order
                {
                    OrderNumber = await _orderRepository.GetNextOrderNumberAsync(),
                    OrderType = (OrderType)((dynamic)cmbOrderType.SelectedItem).Value,
                    EmployeeId = _currentShift.EmployeeId,
                    ShiftId = _currentShift.Id,
                    OrderDate = DateTime.Now,
                    Subtotal = _currentSubtotal,
                    TaxAmount = _currentTax,
                    DiscountAmount = _currentDiscount,
                    TotalAmount = _currentTotal,
                    PaymentMethod = (PaymentMethod)((dynamic)cmbPaymentMethod.SelectedItem).Value,
                    Status = OrderStatus.New,
                    Notes = txtNotes.Text.Trim()
                };
                
                // تحديد بيانات إضافية حسب نوع الطلب
                if (order.OrderType == OrderType.DineIn && cmbTable.SelectedValue != null)
                {
                    order.TableId = (int)cmbTable.SelectedValue;
                }
                else if (order.OrderType == OrderType.Delivery)
                {
                    order.DeliveryAddress = txtDeliveryAddress.Text.Trim();
                    if (string.IsNullOrWhiteSpace(order.DeliveryAddress))
                    {
                        MessageBox.Show("يرجى إدخال عنوان التوصيل", "تنبيه", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }
                }
                
                // حفظ الطلب
                var orderId = await _orderRepository.AddAsync(order);
                
                // حفظ تفاصيل الطلب
                foreach (var detail in _currentOrderDetails)
                {
                    detail.OrderId = orderId;
                    await new OrderDetailRepository().AddAsync(detail);
                }
                
                MessageBox.Show($"تم حفظ الطلب بنجاح\nرقم الطلب: {order.OrderNumber}", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // مسح الطلب الحالي
                ClearCurrentOrder();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة الطلب: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearCurrentOrder()
        {
            _currentOrderDetails.Clear();
            RefreshOrderDetailsGrid();
            UpdateTotals();
            txtNotes.Clear();
            txtDeliveryAddress.Clear();
            numDiscount.Value = 0;
        }

        private void dgvOrderDetails_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.ColumnIndex == dgvOrderDetails.Columns["Delete"].Index && e.RowIndex >= 0)
            {
                _currentOrderDetails.RemoveAt(e.RowIndex);
                RefreshOrderDetailsGrid();
                UpdateTotals();
            }
        }

        private void numDiscount_ValueChanged(object sender, EventArgs e)
        {
            UpdateTotals();
        }

        private void cmbOrderType_SelectedIndexChanged(object sender, EventArgs e)
        {
            var orderType = (OrderType)((dynamic)cmbOrderType.SelectedItem).Value;
            
            // إظهار/إخفاء الحقول حسب نوع الطلب
            lblTable.Visible = cmbTable.Visible = (orderType == OrderType.DineIn);
            lblDeliveryAddress.Visible = txtDeliveryAddress.Visible = (orderType == OrderType.Delivery);
        }

        private void btnClearOrder_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد مسح الطلب الحالي؟", "تأكيد", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                ClearCurrentOrder();
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
