using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.UI.Reports
{
    /// <summary>
    /// شاشة تقارير المخزون
    /// Inventory Reports Form
    /// </summary>
    public partial class InventoryReportsForm : Form
    {
        private readonly IngredientRepository _ingredientRepository;
        private readonly StockMovementRepository _stockMovementRepository;
        private readonly UnitRepository _unitRepository;

        public InventoryReportsForm()
        {
            InitializeComponent();
            _ingredientRepository = new IngredientRepository();
            _stockMovementRepository = new StockMovementRepository();
            _unitRepository = new UnitRepository();

            this.Text = "تقارير المخزون - Inventory Reports";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            InitializeReports();
        }

        private void InitializeReports()
        {
            // إعداد التواريخ الافتراضية
            dtpFromDate.Value = DateTime.Now.Date.AddDays(-30);
            dtpToDate.Value = DateTime.Now.Date;

            // إعداد أنواع التقارير
            cmbReportType.Items.Add(new { Text = "المخزون الحالي", Value = "Current" });
            cmbReportType.Items.Add(new { Text = "حركة المخزون", Value = "Movement" });
            cmbReportType.Items.Add(new { Text = "المخزون المنخفض", Value = "LowStock" });
            cmbReportType.Items.Add(new { Text = "تقييم المخزون", Value = "Valuation" });
            cmbReportType.Items.Add(new { Text = "تقرير الهدر", Value = "Waste" });
            cmbReportType.DisplayMember = "Text";
            cmbReportType.ValueMember = "Value";
            cmbReportType.SelectedIndex = 0;

            // إعداد Chart
            SetupChart();

            // تحميل التقرير الافتراضي
            LoadCurrentInventoryReport();
        }

        private void SetupChart()
        {
            chartInventory.Series.Clear();
            chartInventory.ChartAreas.Clear();

            var chartArea = new ChartArea("InventoryArea");
            chartArea.AxisX.Title = "المكونات";
            chartArea.AxisY.Title = "الكمية";
            chartArea.AxisX.LabelStyle.Angle = -45;
            chartInventory.ChartAreas.Add(chartArea);

            var series = new Series("المخزون");
            series.ChartType = SeriesChartType.Column;
            series.Color = Color.Green;
            chartInventory.Series.Add(series);
        }

        private async void LoadCurrentInventoryReport()
        {
            try
            {
                var ingredients = await _ingredientRepository.GetAllWithUnitsAsync();
                var activeIngredients = ingredients.Where(i => i.IsActive).ToList();

                // إحصائيات عامة
                var totalItems = activeIngredients.Count;
                var totalValue = activeIngredients.Sum(i => i.CurrentStock * i.CostPerUnit);
                var lowStockItems = activeIngredients.Where(i => i.CurrentStock <= i.MinStockAlert).Count();
                var outOfStockItems = activeIngredients.Where(i => i.CurrentStock <= 0).Count();

                lblTotalItems.Text = $"إجمالي المكونات: {totalItems}";
                lblTotalValue.Text = $"قيمة المخزون: {totalValue:C}";
                lblLowStockItems.Text = $"مكونات منخفضة المخزون: {lowStockItems}";
                lblOutOfStockItems.Text = $"مكونات نفدت: {outOfStockItems}";

                // تحميل تفاصيل المخزون
                var inventoryDetails = activeIngredients.Select(i => new
                {
                    IngredientName = i.Name,
                    IngredientNameFr = i.NameFr,
                    CurrentStock = i.CurrentStock,
                    Unit = i.Unit?.Abbreviation ?? "",
                    CostPerUnit = i.CostPerUnit,
                    TotalValue = i.CurrentStock * i.CostPerUnit,
                    MinStockAlert = i.MinStockAlert,
                    Status = GetStockStatus(i.CurrentStock, i.MinStockAlert)
                }).ToList();

                dgvInventoryReport.DataSource = inventoryDetails;

                // تحديث الرسم البياني
                UpdateInventoryChart(activeIngredients);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadStockMovementReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var movements = await _stockMovementRepository.GetMovementsByDateRangeAsync(fromDate, toDate);

                // إحصائيات الحركة
                var totalMovements = movements.Count();
                var incomingMovements = movements.Where(m => m.MovementType == StockMovementType.Purchase ||
                                                           m.MovementType == StockMovementType.Adjustment).Sum(m => m.Quantity);
                var outgoingMovements = movements.Where(m => m.MovementType == StockMovementType.Sale ||
                                                           m.MovementType == StockMovementType.Waste).Sum(m => m.Quantity);

                lblTotalMovements.Text = $"إجمالي الحركات: {totalMovements}";
                lblIncomingStock.Text = $"الوارد: {incomingMovements}";
                lblOutgoingStock.Text = $"الصادر: {outgoingMovements}";

                // تحميل تفاصيل الحركة
                var movementDetails = movements.Select(m => new
                {
                    MovementDate = m.MovementDate.ToString("yyyy-MM-dd HH:mm"),
                    IngredientName = m.Ingredient?.Name ?? "غير محدد",
                    MovementType = GetMovementTypeText(m.MovementType),
                    Quantity = m.Quantity,
                    Unit = m.Ingredient?.Unit?.Abbreviation ?? "",
                    Reference = m.Reference ?? "",
                    Notes = m.Notes ?? "",
                    EmployeeName = m.Employee?.FullName ?? "غير محدد"
                }).ToList();

                dgvInventoryReport.DataSource = movementDetails;

                // تحديث الرسم البياني للحركة
                UpdateMovementChart(movements);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير حركة المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadLowStockReport()
        {
            try
            {
                var lowStockIngredients = await _ingredientRepository.GetLowStockIngredientsAsync();

                lblLowStockCount.Text = $"عدد المكونات منخفضة المخزون: {lowStockIngredients.Count()}";

                var lowStockDetails = lowStockIngredients.Select(i => new
                {
                    IngredientName = i.Name,
                    CurrentStock = i.CurrentStock,
                    MinStockAlert = i.MinStockAlert,
                    Unit = i.Unit?.Abbreviation ?? "",
                    Shortage = i.MinStockAlert - i.CurrentStock,
                    CostPerUnit = i.CostPerUnit,
                    ReorderValue = (i.MinStockAlert - i.CurrentStock) * i.CostPerUnit,
                    Status = i.CurrentStock <= 0 ? "نفد" : "منخفض"
                }).ToList();

                dgvInventoryReport.DataSource = lowStockDetails;

                // تحديث الرسم البياني
                UpdateLowStockChart(lowStockIngredients.ToList());

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير المخزون المنخفض: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadInventoryValuationReport()
        {
            try
            {
                var ingredients = await _ingredientRepository.GetAllWithUnitsAsync();
                var activeIngredients = ingredients.Where(i => i.IsActive).ToList();

                var totalValue = activeIngredients.Sum(i => i.CurrentStock * i.CostPerUnit);
                lblInventoryValue.Text = $"إجمالي قيمة المخزون: {totalValue:C}";

                var valuationDetails = activeIngredients.Select(i => new
                {
                    IngredientName = i.Name,
                    CurrentStock = i.CurrentStock,
                    Unit = i.Unit?.Abbreviation ?? "",
                    CostPerUnit = i.CostPerUnit,
                    TotalValue = i.CurrentStock * i.CostPerUnit,
                    Percentage = totalValue > 0 ? (i.CurrentStock * i.CostPerUnit / totalValue * 100) : 0
                }).OrderByDescending(x => x.TotalValue).ToList();

                dgvInventoryReport.DataSource = valuationDetails;

                // تحديث الرسم البياني
                UpdateValuationChart(valuationDetails);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير تقييم المخزون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateInventoryChart(List<Ingredient> ingredients)
        {
            chartInventory.Series["المخزون"].Points.Clear();

            var topIngredients = ingredients
                .OrderByDescending(i => i.CurrentStock * i.CostPerUnit)
                .Take(10)
                .ToList();

            foreach (var ingredient in topIngredients)
            {
                chartInventory.Series["المخزون"].Points.AddXY(ingredient.Name, ingredient.CurrentStock);
            }
        }

        private void UpdateMovementChart(IEnumerable<StockMovement> movements)
        {
            chartInventory.Series.Clear();
            chartInventory.Series.Add(new Series("الوارد") { ChartType = SeriesChartType.Column, Color = Color.Green });
            chartInventory.Series.Add(new Series("الصادر") { ChartType = SeriesChartType.Column, Color = Color.Red });

            var movementsByDate = movements
                .GroupBy(m => m.MovementDate.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    Incoming = g.Where(m => m.MovementType == StockMovementType.Purchase ||
                                          m.MovementType == StockMovementType.Adjustment).Sum(m => m.Quantity),
                    Outgoing = g.Where(m => m.MovementType == StockMovementType.Sale ||
                                          m.MovementType == StockMovementType.Waste).Sum(m => m.Quantity)
                })
                .OrderBy(x => x.Date)
                .ToList();

            foreach (var item in movementsByDate)
            {
                chartInventory.Series["الوارد"].Points.AddXY(item.Date.ToString("MM/dd"), item.Incoming);
                chartInventory.Series["الصادر"].Points.AddXY(item.Date.ToString("MM/dd"), item.Outgoing);
            }
        }

        private void UpdateLowStockChart(List<Ingredient> lowStockIngredients)
        {
            chartInventory.Series["المخزون"].Points.Clear();

            foreach (var ingredient in lowStockIngredients.Take(10))
            {
                var point = chartInventory.Series["المخزون"].Points.AddXY(ingredient.Name, ingredient.CurrentStock);
                chartInventory.Series["المخزون"].Points[point].Color =
                    ingredient.CurrentStock <= 0 ? Color.Red : Color.Orange;
            }
        }

        private void UpdateValuationChart(List<dynamic> valuationData)
        {
            chartInventory.Series["المخزون"].Points.Clear();
            chartInventory.Series["المخزون"].ChartType = SeriesChartType.Pie;

            foreach (var item in valuationData.Take(10))
            {
                chartInventory.Series["المخزون"].Points.AddXY(item.IngredientName, item.TotalValue);
            }
        }

        private string GetStockStatus(decimal currentStock, decimal minStockAlert)
        {
            if (currentStock <= 0)
                return "نفد";
            else if (currentStock <= minStockAlert)
                return "منخفض";
            else
                return "طبيعي";
        }

        private string GetMovementTypeText(StockMovementType movementType)
        {
            return movementType switch
            {
                StockMovementType.Purchase => "شراء",
                StockMovementType.Sale => "بيع",
                StockMovementType.Adjustment => "تعديل",
                StockMovementType.Waste => "هدر",
                StockMovementType.Transfer => "نقل",
                _ => "غير محدد"
            };
        }

        private async void btnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                var reportType = ((dynamic)cmbReportType.SelectedItem).Value.ToString();

                switch (reportType)
                {
                    case "Current":
                        await LoadCurrentInventoryReport();
                        break;
                    case "Movement":
                        await LoadStockMovementReport();
                        break;
                    case "LowStock":
                        await LoadLowStockReport();
                        break;
                    case "Valuation":
                        await LoadInventoryValuationReport();
                        break;
                    case "Waste":
                        await LoadWasteReport();
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadWasteReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var wasteMovements = await _stockMovementRepository.GetWasteMovementsAsync(fromDate, toDate);

                var totalWasteValue = wasteMovements.Sum(w => w.Quantity * (w.Ingredient?.CostPerUnit ?? 0));
                lblWasteValue.Text = $"إجمالي قيمة الهدر: {totalWasteValue:C}";

                var wasteDetails = wasteMovements.Select(w => new
                {
                    WasteDate = w.MovementDate.ToString("yyyy-MM-dd HH:mm"),
                    IngredientName = w.Ingredient?.Name ?? "غير محدد",
                    Quantity = w.Quantity,
                    Unit = w.Ingredient?.Unit?.Abbreviation ?? "",
                    CostPerUnit = w.Ingredient?.CostPerUnit ?? 0,
                    TotalValue = w.Quantity * (w.Ingredient?.CostPerUnit ?? 0),
                    Reason = w.Notes ?? "غير محدد",
                    EmployeeName = w.Employee?.FullName ?? "غير محدد"
                }).ToList();

                dgvInventoryReport.DataSource = wasteDetails;

                // تحديث الرسم البياني
                UpdateWasteChart(wasteMovements);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير الهدر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateWasteChart(IEnumerable<StockMovement> wasteMovements)
        {
            chartInventory.Series["المخزون"].Points.Clear();

            var wasteByIngredient = wasteMovements
                .GroupBy(w => w.Ingredient?.Name ?? "غير محدد")
                .Select(g => new
                {
                    IngredientName = g.Key,
                    TotalWaste = g.Sum(w => w.Quantity),
                    TotalValue = g.Sum(w => w.Quantity * (w.Ingredient?.CostPerUnit ?? 0))
                })
                .OrderByDescending(x => x.TotalValue)
                .Take(10)
                .ToList();

            foreach (var item in wasteByIngredient)
            {
                chartInventory.Series["المخزون"].Points.AddXY(item.IngredientName, item.TotalValue);
            }
        }

        private void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    FileName = $"InventoryReport_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToExcel(saveDialog.FileName);
                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToExcel(string fileName)
        {
            // كود تصدير Excel مشابه لتقارير المبيعات
            using (var package = new OfficeOpenXml.ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("تقرير المخزون");

                // إضافة العناوين والبيانات
                var data = (System.Collections.IList)dgvInventoryReport.DataSource;
                if (data != null && data.Count > 0)
                {
                    // إضافة العناوين
                    for (int i = 0; i < dgvInventoryReport.Columns.Count; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = dgvInventoryReport.Columns[i].HeaderText;
                    }

                    // إضافة البيانات
                    for (int i = 0; i < data.Count; i++)
                    {
                        var item = data[i];
                        var properties = item.GetType().GetProperties();

                        for (int j = 0; j < properties.Length; j++)
                        {
                            worksheet.Cells[i + 2, j + 1].Value = properties[j].GetValue(item);
                        }
                    }
                }

                package.SaveAs(new System.IO.FileInfo(fileName));
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("تم إرسال التقرير للطباعة", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cmbReportType_SelectedIndexChanged(object sender, EventArgs e)
        {
            var reportType = ((dynamic)cmbReportType.SelectedItem).Value.ToString();

            // إظهار/إخفاء عناصر التحكم في التاريخ حسب نوع التقرير
            bool showDateRange = reportType == "Movement" || reportType == "Waste";
            dtpFromDate.Enabled = showDateRange;
            dtpToDate.Enabled = showDateRange;
            lblFromDate.Enabled = showDateRange;
            lblToDate.Enabled = showDateRange;
        }
    }
}
