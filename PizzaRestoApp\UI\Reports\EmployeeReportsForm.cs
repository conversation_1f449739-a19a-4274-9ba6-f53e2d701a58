using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;
using System.Data;
using System.Windows.Forms.DataVisualization.Charting;

namespace PizzaRestoApp.UI.Reports
{
    /// <summary>
    /// شاشة تقارير الموظفين
    /// Employee Reports Form
    /// </summary>
    public partial class EmployeeReportsForm : Form
    {
        private readonly EmployeeRepository _employeeRepository;
        private readonly EmployeeAttendanceRepository _attendanceRepository;
        private readonly EmployeeCommissionRepository _commissionRepository;
        private readonly OrderRepository _orderRepository;

        public EmployeeReportsForm()
        {
            InitializeComponent();
            _employeeRepository = new EmployeeRepository();
            _attendanceRepository = new EmployeeAttendanceRepository();
            _commissionRepository = new EmployeeCommissionRepository();
            _orderRepository = new OrderRepository();

            this.Text = "تقارير الموظفين - Employee Reports";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            InitializeReports();
        }

        private async void InitializeReports()
        {
            // إعداد التواريخ الافتراضية
            dtpFromDate.Value = DateTime.Now.Date.AddDays(-30);
            dtpToDate.Value = DateTime.Now.Date;

            // تحميل الموظفين
            var employees = await _employeeRepository.GetActiveEmployeesAsync();
            cmbEmployee.DataSource = employees.ToList();
            cmbEmployee.DisplayMember = "FullName";
            cmbEmployee.ValueMember = "Id";
            cmbEmployee.SelectedIndex = -1;

            // إعداد أنواع التقارير
            cmbReportType.Items.Add(new { Text = "تقرير الحضور", Value = "Attendance" });
            cmbReportType.Items.Add(new { Text = "تقرير الأداء", Value = "Performance" });
            cmbReportType.Items.Add(new { Text = "تقرير العمولات", Value = "Commission" });
            cmbReportType.Items.Add(new { Text = "تقرير ساعات العمل", Value = "WorkHours" });
            cmbReportType.Items.Add(new { Text = "تقرير شامل", Value = "Comprehensive" });
            cmbReportType.DisplayMember = "Text";
            cmbReportType.ValueMember = "Value";
            cmbReportType.SelectedIndex = 0;

            // إعداد Chart
            SetupChart();

            // تحميل التقرير الافتراضي
            LoadAttendanceReport();
        }

        private void SetupChart()
        {
            chartEmployee.Series.Clear();
            chartEmployee.ChartAreas.Clear();

            var chartArea = new ChartArea("EmployeeArea");
            chartArea.AxisX.Title = "الموظفين";
            chartArea.AxisY.Title = "القيم";
            chartArea.AxisX.LabelStyle.Angle = -45;
            chartEmployee.ChartAreas.Add(chartArea);

            var series = new Series("البيانات");
            series.ChartType = SeriesChartType.Column;
            series.Color = Color.SteelBlue;
            chartEmployee.Series.Add(series);
        }

        private async void LoadAttendanceReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var attendanceRecords = await _attendanceRepository.GetAttendanceByDateRangeAsync(fromDate, toDate);

                if (cmbEmployee.SelectedValue != null)
                {
                    var employeeId = (int)cmbEmployee.SelectedValue;
                    attendanceRecords = attendanceRecords.Where(a => a.EmployeeId == employeeId);
                }

                // إحصائيات الحضور
                var totalDays = attendanceRecords.Count();
                var presentDays = attendanceRecords.Where(a => a.CheckInTime.HasValue).Count();
                var lateDays = attendanceRecords.Where(a => a.IsLate).Count();
                var totalWorkHours = attendanceRecords.Sum(a => a.WorkHours);

                lblTotalDays.Text = $"إجمالي الأيام: {totalDays}";
                lblPresentDays.Text = $"أيام الحضور: {presentDays}";
                lblLateDays.Text = $"أيام التأخير: {lateDays}";
                lblTotalWorkHours.Text = $"إجمالي ساعات العمل: {totalWorkHours:F1}";

                // تحميل تفاصيل الحضور
                var attendanceDetails = attendanceRecords.Select(a => new
                {
                    EmployeeName = a.Employee?.FullName ?? "غير محدد",
                    AttendanceDate = a.AttendanceDate.ToString("yyyy-MM-dd"),
                    CheckInTime = a.CheckInTime?.ToString("HH:mm") ?? "لم يحضر",
                    CheckOutTime = a.CheckOutTime?.ToString("HH:mm") ?? "لم ينصرف",
                    WorkHours = a.WorkHours,
                    IsLate = a.IsLate ? "متأخر" : "في الوقت",
                    Notes = a.Notes ?? ""
                }).ToList();

                dgvEmployeeReport.DataSource = attendanceDetails;

                // تحديث الرسم البياني
                UpdateAttendanceChart(attendanceRecords);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير الحضور: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadPerformanceReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var employees = await _employeeRepository.GetActiveEmployeesAsync();
                var performanceData = new List<dynamic>();

                foreach (var employee in employees)
                {
                    if (cmbEmployee.SelectedValue != null && employee.Id != (int)cmbEmployee.SelectedValue)
                        continue;

                    var orders = await _orderRepository.GetOrdersByEmployeeAsync(employee.Id, fromDate, toDate);
                    var completedOrders = orders.Where(o => o.Status == Utils.OrderStatus.Completed);

                    var totalOrders = completedOrders.Count();
                    var totalSales = completedOrders.Sum(o => o.TotalAmount);
                    var averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

                    performanceData.Add(new
                    {
                        EmployeeName = employee.FullName,
                        TotalOrders = totalOrders,
                        TotalSales = totalSales,
                        AverageOrderValue = averageOrderValue,
                        Position = employee.Role?.Name ?? "غير محدد"
                    });
                }

                dgvEmployeeReport.DataSource = performanceData;

                // إحصائيات الأداء
                var totalEmployeeOrders = performanceData.Sum(p => (int)p.TotalOrders);
                var totalEmployeeSales = performanceData.Sum(p => (decimal)p.TotalSales);
                var avgOrdersPerEmployee = performanceData.Count > 0 ? totalEmployeeOrders / performanceData.Count : 0;

                lblTotalOrders.Text = $"إجمالي الطلبات: {totalEmployeeOrders}";
                lblTotalSales.Text = $"إجمالي المبيعات: {totalEmployeeSales:C}";
                lblAvgOrdersPerEmployee.Text = $"متوسط الطلبات لكل موظف: {avgOrdersPerEmployee}";

                // تحديث الرسم البياني
                UpdatePerformanceChart(performanceData);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير الأداء: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadCommissionReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var commissions = await _commissionRepository.GetCommissionsByDateRangeAsync(fromDate, toDate);

                if (cmbEmployee.SelectedValue != null)
                {
                    var employeeId = (int)cmbEmployee.SelectedValue;
                    commissions = commissions.Where(c => c.EmployeeId == employeeId);
                }

                // إحصائيات العمولات
                var totalCommissions = commissions.Sum(c => c.CommissionAmount);
                var totalSalesForCommission = commissions.Sum(c => c.SalesAmount);
                var avgCommissionRate = commissions.Count() > 0 ? commissions.Average(c => c.CommissionRate) : 0;

                lblTotalCommissions.Text = $"إجمالي العمولات: {totalCommissions:C}";
                lblTotalSalesForCommission.Text = $"إجمالي المبيعات: {totalSalesForCommission:C}";
                lblAvgCommissionRate.Text = $"متوسط معدل العمولة: {avgCommissionRate:P}";

                // تحميل تفاصيل العمولات
                var commissionDetails = commissions.Select(c => new
                {
                    EmployeeName = c.Employee?.FullName ?? "غير محدد",
                    CommissionDate = c.CommissionDate.ToString("yyyy-MM-dd"),
                    SalesAmount = c.SalesAmount,
                    CommissionRate = c.CommissionRate,
                    CommissionAmount = c.CommissionAmount,
                    OrderNumber = c.Order?.OrderNumber ?? "غير محدد",
                    Notes = c.Notes ?? ""
                }).ToList();

                dgvEmployeeReport.DataSource = commissionDetails;

                // تحديث الرسم البياني
                UpdateCommissionChart(commissions);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير العمولات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateAttendanceChart(IEnumerable<EmployeeAttendance> attendanceRecords)
        {
            chartEmployee.Series["البيانات"].Points.Clear();

            var attendanceByEmployee = attendanceRecords
                .GroupBy(a => a.Employee?.FullName ?? "غير محدد")
                .Select(g => new
                {
                    EmployeeName = g.Key,
                    TotalHours = g.Sum(a => a.WorkHours),
                    PresentDays = g.Count(a => a.CheckInTime.HasValue)
                })
                .OrderByDescending(x => x.TotalHours)
                .Take(10)
                .ToList();

            foreach (var item in attendanceByEmployee)
            {
                chartEmployee.Series["البيانات"].Points.AddXY(item.EmployeeName, item.TotalHours);
            }
        }

        private void UpdatePerformanceChart(List<dynamic> performanceData)
        {
            chartEmployee.Series["البيانات"].Points.Clear();

            foreach (var item in performanceData.Take(10))
            {
                chartEmployee.Series["البيانات"].Points.AddXY(item.EmployeeName, item.TotalSales);
            }
        }

        private void UpdateCommissionChart(IEnumerable<EmployeeCommission> commissions)
        {
            chartEmployee.Series["البيانات"].Points.Clear();

            var commissionByEmployee = commissions
                .GroupBy(c => c.Employee?.FullName ?? "غير محدد")
                .Select(g => new
                {
                    EmployeeName = g.Key,
                    TotalCommission = g.Sum(c => c.CommissionAmount)
                })
                .OrderByDescending(x => x.TotalCommission)
                .Take(10)
                .ToList();

            foreach (var item in commissionByEmployee)
            {
                chartEmployee.Series["البيانات"].Points.AddXY(item.EmployeeName, item.TotalCommission);
            }
        }

        private async void LoadWorkHoursReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var attendanceRecords = await _attendanceRepository.GetAttendanceByDateRangeAsync(fromDate, toDate);

                if (cmbEmployee.SelectedValue != null)
                {
                    var employeeId = (int)cmbEmployee.SelectedValue;
                    attendanceRecords = attendanceRecords.Where(a => a.EmployeeId == employeeId);
                }

                var workHoursData = attendanceRecords
                    .GroupBy(a => a.Employee?.FullName ?? "غير محدد")
                    .Select(g => new
                    {
                        EmployeeName = g.Key,
                        TotalWorkHours = g.Sum(a => a.WorkHours),
                        TotalDays = g.Count(),
                        AverageHoursPerDay = g.Count() > 0 ? g.Sum(a => a.WorkHours) / g.Count() : 0,
                        OvertimeHours = g.Sum(a => Math.Max(0, a.WorkHours - 8)), // افتراض 8 ساعات عمل عادية
                        Position = g.First().Employee?.Role?.Name ?? "غير محدد"
                    })
                    .OrderByDescending(x => x.TotalWorkHours)
                    .ToList();

                dgvEmployeeReport.DataSource = workHoursData;

                // إحصائيات ساعات العمل
                var totalWorkHours = workHoursData.Sum(w => w.TotalWorkHours);
                var totalOvertimeHours = workHoursData.Sum(w => w.OvertimeHours);
                var avgHoursPerEmployee = workHoursData.Count > 0 ? totalWorkHours / workHoursData.Count : 0;

                lblTotalWorkHours.Text = $"إجمالي ساعات العمل: {totalWorkHours:F1}";
                lblOvertimeHours.Text = $"ساعات العمل الإضافي: {totalOvertimeHours:F1}";
                lblAvgHoursPerEmployee.Text = $"متوسط الساعات لكل موظف: {avgHoursPerEmployee:F1}";

                // تحديث الرسم البياني
                UpdateWorkHoursChart(workHoursData);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير ساعات العمل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateWorkHoursChart(List<dynamic> workHoursData)
        {
            chartEmployee.Series.Clear();
            chartEmployee.Series.Add(new Series("ساعات عادية") { ChartType = SeriesChartType.Column, Color = Color.Green });
            chartEmployee.Series.Add(new Series("ساعات إضافية") { ChartType = SeriesChartType.Column, Color = Color.Orange });

            foreach (var item in workHoursData.Take(10))
            {
                var regularHours = item.TotalWorkHours - item.OvertimeHours;
                chartEmployee.Series["ساعات عادية"].Points.AddXY(item.EmployeeName, regularHours);
                chartEmployee.Series["ساعات إضافية"].Points.AddXY(item.EmployeeName, item.OvertimeHours);
            }
        }

        private async void LoadComprehensiveReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                var employees = await _employeeRepository.GetActiveEmployeesAsync();
                var comprehensiveData = new List<dynamic>();

                foreach (var employee in employees)
                {
                    if (cmbEmployee.SelectedValue != null && employee.Id != (int)cmbEmployee.SelectedValue)
                        continue;

                    // بيانات الحضور
                    var attendance = await _attendanceRepository.GetAttendanceByEmployeeAsync(employee.Id, fromDate, toDate);
                    var totalWorkHours = attendance.Sum(a => a.WorkHours);
                    var presentDays = attendance.Count(a => a.CheckInTime.HasValue);
                    var lateDays = attendance.Count(a => a.IsLate);

                    // بيانات الأداء
                    var orders = await _orderRepository.GetOrdersByEmployeeAsync(employee.Id, fromDate, toDate);
                    var completedOrders = orders.Where(o => o.Status == Utils.OrderStatus.Completed);
                    var totalOrders = completedOrders.Count();
                    var totalSales = completedOrders.Sum(o => o.TotalAmount);

                    // بيانات العمولات
                    var commissions = await _commissionRepository.GetCommissionsByEmployeeAsync(employee.Id, fromDate, toDate);
                    var totalCommissions = commissions.Sum(c => c.CommissionAmount);

                    comprehensiveData.Add(new
                    {
                        EmployeeName = employee.FullName,
                        Position = employee.Role?.Name ?? "غير محدد",
                        TotalWorkHours = totalWorkHours,
                        PresentDays = presentDays,
                        LateDays = lateDays,
                        TotalOrders = totalOrders,
                        TotalSales = totalSales,
                        TotalCommissions = totalCommissions,
                        AttendanceRate = attendance.Count() > 0 ? (decimal)presentDays / attendance.Count() * 100 : 0
                    });
                }

                dgvEmployeeReport.DataSource = comprehensiveData;

                // إحصائيات شاملة
                var totalEmployees = comprehensiveData.Count;
                var avgAttendanceRate = comprehensiveData.Count > 0 ? comprehensiveData.Average(c => (decimal)c.AttendanceRate) : 0;
                var totalAllSales = comprehensiveData.Sum(c => (decimal)c.TotalSales);
                var totalAllCommissions = comprehensiveData.Sum(c => (decimal)c.TotalCommissions);

                lblTotalEmployees.Text = $"إجمالي الموظفين: {totalEmployees}";
                lblAvgAttendanceRate.Text = $"متوسط معدل الحضور: {avgAttendanceRate:F1}%";
                lblTotalAllSales.Text = $"إجمالي المبيعات: {totalAllSales:C}";
                lblTotalAllCommissions.Text = $"إجمالي العمولات: {totalAllCommissions:C}";

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التقرير الشامل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                var reportType = ((dynamic)cmbReportType.SelectedItem).Value.ToString();

                switch (reportType)
                {
                    case "Attendance":
                        await LoadAttendanceReport();
                        break;
                    case "Performance":
                        await LoadPerformanceReport();
                        break;
                    case "Commission":
                        await LoadCommissionReport();
                        break;
                    case "WorkHours":
                        await LoadWorkHoursReport();
                        break;
                    case "Comprehensive":
                        await LoadComprehensiveReport();
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    FileName = $"EmployeeReport_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToExcel(saveDialog.FileName);
                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToExcel(string fileName)
        {
            using (var package = new OfficeOpenXml.ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("تقرير الموظفين");

                var data = (System.Collections.IList)dgvEmployeeReport.DataSource;
                if (data != null && data.Count > 0)
                {
                    for (int i = 0; i < dgvEmployeeReport.Columns.Count; i++)
                    {
                        worksheet.Cells[1, i + 1].Value = dgvEmployeeReport.Columns[i].HeaderText;
                    }

                    for (int i = 0; i < data.Count; i++)
                    {
                        var item = data[i];
                        var properties = item.GetType().GetProperties();

                        for (int j = 0; j < properties.Length; j++)
                        {
                            worksheet.Cells[i + 2, j + 1].Value = properties[j].GetValue(item);
                        }
                    }
                }

                package.SaveAs(new System.IO.FileInfo(fileName));
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    MessageBox.Show("تم إرسال التقرير للطباعة", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cmbEmployee_SelectedIndexChanged(object sender, EventArgs e)
        {
            // إعادة تحميل التقرير عند تغيير الموظف المحدد
            btnGenerateReport_Click(sender, e);
        }
    }
}
