using System;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج المكون/المادة الخام
    /// Ingredient/Raw Material Model
    /// </summary>
    public class Ingredient
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameFr { get; set; } = string.Empty;
        public int UnitId { get; set; }
        public decimal CostPerUnit { get; set; }
        public decimal CurrentStock { get; set; } = 0;
        public decimal MinStockAlert { get; set; } = 0;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public Unit? Unit { get; set; }

        /// <summary>
        /// التحقق من انخفاض المخزون
        /// Check if stock is low
        /// </summary>
        public bool IsLowStock => CurrentStock <= MinStockAlert;

        /// <summary>
        /// التحقق من نفاد المخزون
        /// Check if out of stock
        /// </summary>
        public bool IsOutOfStock => CurrentStock <= 0;

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   !string.IsNullOrWhiteSpace(NameFr) &&
                   UnitId > 0 &&
                   CostPerUnit >= 0 &&
                   CurrentStock >= 0 &&
                   MinStockAlert >= 0;
        }

        /// <summary>
        /// تحديث المخزون
        /// Update Stock
        /// </summary>
        /// <param name="quantity">الكمية (موجبة للإضافة، سالبة للخصم)</param>
        public void UpdateStock(decimal quantity)
        {
            CurrentStock += quantity;
            if (CurrentStock < 0)
                CurrentStock = 0;
        }
    }
}
