using System;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج مكونات الطبق
    /// Dish Ingredient Model
    /// </summary>
    public class DishIngredient
    {
        public int Id { get; set; }
        public int DishId { get; set; }
        public int IngredientId { get; set; }
        public decimal Quantity { get; set; }
        public DateTime CreatedAt { get; set; }

        // Navigation Properties
        public Dish? Dish { get; set; }
        public Ingredient? Ingredient { get; set; }

        /// <summary>
        /// حساب تكلفة هذا المكون في الطبق
        /// Calculate cost of this ingredient in the dish
        /// </summary>
        public decimal CalculateCost()
        {
            return Quantity * (Ingredient?.CostPerUnit ?? 0);
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            return DishId > 0 &&
                   IngredientId > 0 &&
                   Quantity > 0;
        }
    }
}
