using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PizzaRestoApp.Models;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع المشتريات
    /// Purchase Repository
    /// </summary>
    public class PurchaseRepository : BaseRepository<Purchase>
    {
        public PurchaseRepository() : base("purchases") { }

        /// <summary>
        /// إضافة مشتريات جديدة
        /// Add new purchase
        /// </summary>
        public override async Task<int> AddAsync(Purchase purchase)
        {
            var sql = @"
                INSERT INTO purchases (purchase_number, supplier_id, employee_id, purchase_date, 
                                     total_amount, notes, status, created_at, updated_at)
                VALUES (@PurchaseNumber, @SupplierId, @EmployeeId, @PurchaseDate, 
                        @TotalAmount, @Notes, @Status, @CreatedAt, @UpdatedAt);
                SELECT LAST_INSERT_ID();";

            purchase.CreatedAt = DateTime.Now;
            purchase.UpdatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, purchase);
        }

        /// <summary>
        /// تحديث مشتريات موجودة
        /// Update existing purchase
        /// </summary>
        public override async Task<bool> UpdateAsync(Purchase purchase)
        {
            var sql = @"
                UPDATE purchases 
                SET purchase_number = @PurchaseNumber, supplier_id = @SupplierId, 
                    employee_id = @EmployeeId, purchase_date = @PurchaseDate,
                    total_amount = @TotalAmount, notes = @Notes, status = @Status, 
                    updated_at = @UpdatedAt
                WHERE id = @Id";

            purchase.UpdatedAt = DateTime.Now;
            var affectedRows = await ExecuteAsync(sql, purchase);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على مشتريات مع تفاصيل المورد والموظف
        /// Get purchase with supplier and employee details
        /// </summary>
        public async Task<Purchase?> GetByIdWithDetailsAsync(int id)
        {
            var sql = @"
                SELECT p.*, 
                       s.name as SupplierName, s.contact_person as SupplierContactPerson,
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM purchases p
                INNER JOIN suppliers s ON p.supplier_id = s.id
                INNER JOIN employees e ON p.employee_id = e.id
                WHERE p.id = @Id";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Purchase, Supplier, Employee, Purchase>(
                sql,
                (purchase, supplier, employee) =>
                {
                    purchase.Supplier = supplier;
                    purchase.Employee = employee;
                    return purchase;
                },
                new { Id = id },
                splitOn: "SupplierName,EmployeeFirstName"
            );

            var purchaseResult = result.FirstOrDefault();
            if (purchaseResult != null)
            {
                purchaseResult.PurchaseDetails = (await GetPurchaseDetailsAsync(id)).ToList();
            }

            return purchaseResult;
        }

        /// <summary>
        /// الحصول على تفاصيل المشتريات
        /// Get purchase details
        /// </summary>
        public async Task<IEnumerable<PurchaseDetail>> GetPurchaseDetailsAsync(int purchaseId)
        {
            var sql = @"
                SELECT pd.*, i.name as IngredientName, i.name_fr as IngredientNameFr,
                       u.name as UnitName, u.abbreviation as UnitAbbreviation
                FROM purchase_details pd
                INNER JOIN ingredients i ON pd.ingredient_id = i.id
                INNER JOIN units u ON i.unit_id = u.id
                WHERE pd.purchase_id = @PurchaseId";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<PurchaseDetail, Ingredient, Unit, PurchaseDetail>(
                sql,
                (purchaseDetail, ingredient, unit) =>
                {
                    ingredient.Unit = unit;
                    purchaseDetail.Ingredient = ingredient;
                    return purchaseDetail;
                },
                new { PurchaseId = purchaseId },
                splitOn: "IngredientName,UnitName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على جميع المشتريات مع التفاصيل
        /// Get all purchases with details
        /// </summary>
        public async Task<IEnumerable<Purchase>> GetAllWithDetailsAsync()
        {
            var sql = @"
                SELECT p.*, 
                       s.name as SupplierName, s.contact_person as SupplierContactPerson,
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM purchases p
                INNER JOIN suppliers s ON p.supplier_id = s.id
                INNER JOIN employees e ON p.employee_id = e.id
                ORDER BY p.purchase_date DESC";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Purchase, Supplier, Employee, Purchase>(
                sql,
                (purchase, supplier, employee) =>
                {
                    purchase.Supplier = supplier;
                    purchase.Employee = employee;
                    return purchase;
                },
                splitOn: "SupplierName,EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على المشتريات حسب الحالة
        /// Get purchases by status
        /// </summary>
        public async Task<IEnumerable<Purchase>> GetByStatusAsync(PurchaseStatus status)
        {
            var sql = @"
                SELECT p.*, 
                       s.name as SupplierName, s.contact_person as SupplierContactPerson,
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM purchases p
                INNER JOIN suppliers s ON p.supplier_id = s.id
                INNER JOIN employees e ON p.employee_id = e.id
                WHERE p.status = @Status
                ORDER BY p.purchase_date DESC";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Purchase, Supplier, Employee, Purchase>(
                sql,
                (purchase, supplier, employee) =>
                {
                    purchase.Supplier = supplier;
                    purchase.Employee = employee;
                    return purchase;
                },
                new { Status = status },
                splitOn: "SupplierName,EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على المشتريات حسب التاريخ
        /// Get purchases by date range
        /// </summary>
        public async Task<IEnumerable<Purchase>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            var sql = @"
                SELECT p.*, 
                       s.name as SupplierName, s.contact_person as SupplierContactPerson,
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM purchases p
                INNER JOIN suppliers s ON p.supplier_id = s.id
                INNER JOIN employees e ON p.employee_id = e.id
                WHERE DATE(p.purchase_date) BETWEEN @StartDate AND @EndDate
                ORDER BY p.purchase_date DESC";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Purchase, Supplier, Employee, Purchase>(
                sql,
                (purchase, supplier, employee) =>
                {
                    purchase.Supplier = supplier;
                    purchase.Employee = employee;
                    return purchase;
                },
                new { StartDate = startDate.Date, EndDate = endDate.Date },
                splitOn: "SupplierName,EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// تحديث حالة المشتريات
        /// Update purchase status
        /// </summary>
        public async Task<bool> UpdateStatusAsync(int purchaseId, PurchaseStatus status)
        {
            var sql = @"
                UPDATE purchases 
                SET status = @Status, updated_at = @UpdatedAt
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, new 
            { 
                Id = purchaseId, 
                Status = status, 
                UpdatedAt = DateTime.Now 
            });
            
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على رقم المشتريات التالي
        /// Get next purchase number
        /// </summary>
        public async Task<string> GetNextPurchaseNumberAsync()
        {
            var today = DateTime.Now.ToString("yyyyMMdd");
            var sql = @"
                SELECT COUNT(*) + 1 
                FROM purchases 
                WHERE DATE(purchase_date) = CURDATE()";

            var purchaseCount = await QueryFirstOrDefaultAsync<int>(sql);
            return $"PUR-{today}-{purchaseCount:D4}";
        }
    }
}
