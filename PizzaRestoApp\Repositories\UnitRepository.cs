using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع وحدات القياس
    /// Unit Repository
    /// </summary>
    public class UnitRepository : BaseRepository<Unit>
    {
        public UnitRepository() : base("units") { }

        /// <summary>
        /// إضافة وحدة قياس جديدة
        /// Add new unit
        /// </summary>
        public override async Task<int> AddAsync(Unit unit)
        {
            var sql = @"
                INSERT INTO units (name, name_fr, abbreviation, created_at)
                VALUES (@Name, @NameFr, @Abbreviation, @CreatedAt);
                SELECT LAST_INSERT_ID();";

            unit.CreatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, unit);
        }

        /// <summary>
        /// تحديث وحدة قياس موجودة
        /// Update existing unit
        /// </summary>
        public override async Task<bool> UpdateAsync(Unit unit)
        {
            var sql = @"
                UPDATE units 
                SET name = @Name, name_fr = @NameFr, abbreviation = @Abbreviation
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, unit);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على وحدة قياس بالاسم
        /// Get unit by name
        /// </summary>
        public async Task<Unit?> GetByNameAsync(string name)
        {
            var sql = "SELECT * FROM units WHERE name = @Name";
            return await QueryFirstOrDefaultAsync(sql, new { Name = name });
        }

        /// <summary>
        /// الحصول على وحدة قياس بالاختصار
        /// Get unit by abbreviation
        /// </summary>
        public async Task<Unit?> GetByAbbreviationAsync(string abbreviation)
        {
            var sql = "SELECT * FROM units WHERE abbreviation = @Abbreviation";
            return await QueryFirstOrDefaultAsync(sql, new { Abbreviation = abbreviation });
        }
    }
}
