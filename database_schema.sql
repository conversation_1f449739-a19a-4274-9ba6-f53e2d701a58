-- ===================================================
-- نظام إدارة مطعم وبيزيريا - قاعدة البيانات
-- Restaurant & Pizzeria Management System Database
-- ===================================================

CREATE DATABASE IF NOT EXISTS pizzaresto_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE pizzaresto_db;

-- ===================================================
-- جدول الأدوار والصلاحيات
-- Roles and Permissions Table
-- ===================================================
CREATE TABLE roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    name_fr VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===================================================
-- جدول الموظفين
-- Employees Table
-- ===================================================
CREATE TABLE employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_code VARCHAR(20) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    role_id INT NOT NULL,
    hire_date DATE NOT NULL,
    salary DECIMAL(10,2),
    percentage_rate DECIMAL(5,2) DEFAULT 0.00, -- نسبة البرسنتاج
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- ===================================================
-- جدول وحدات القياس
-- Units of Measurement Table
-- ===================================================
CREATE TABLE units (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE,
    name_fr VARCHAR(50) NOT NULL,
    abbreviation VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===================================================
-- جدول المكونات/المواد الخام
-- Ingredients/Raw Materials Table
-- ===================================================
CREATE TABLE ingredients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_fr VARCHAR(100) NOT NULL,
    unit_id INT NOT NULL,
    cost_per_unit DECIMAL(10,3) NOT NULL,
    current_stock DECIMAL(10,3) DEFAULT 0,
    min_stock_alert DECIMAL(10,3) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (unit_id) REFERENCES units(id)
);

-- ===================================================
-- جدول فئات الأطباق
-- Dish Categories Table
-- ===================================================
CREATE TABLE dish_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_fr VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===================================================
-- جدول الأطباق
-- Dishes Table
-- ===================================================
CREATE TABLE dishes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    name_fr VARCHAR(100) NOT NULL,
    category_id INT NOT NULL,
    description TEXT,
    base_cost DECIMAL(10,2) DEFAULT 0, -- التكلفة الأساسية (محسوبة تلقائياً)
    profit_margin DECIMAL(5,2) DEFAULT 30.00, -- هامش الربح بالنسبة المئوية
    selling_price DECIMAL(10,2) NOT NULL, -- سعر البيع (محسوب تلقائياً)
    preparation_time INT DEFAULT 15, -- وقت التحضير بالدقائق
    is_available BOOLEAN DEFAULT TRUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES dish_categories(id)
);

-- ===================================================
-- جدول مكونات الأطباق
-- Dish Ingredients Table
-- ===================================================
CREATE TABLE dish_ingredients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    dish_id INT NOT NULL,
    ingredient_id INT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (dish_id) REFERENCES dishes(id) ON DELETE CASCADE,
    FOREIGN KEY (ingredient_id) REFERENCES ingredients(id),
    UNIQUE KEY unique_dish_ingredient (dish_id, ingredient_id)
);

-- ===================================================
-- جدول الموردين
-- Suppliers Table
-- ===================================================
CREATE TABLE suppliers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===================================================
-- جدول المشتريات
-- Purchases Table
-- ===================================================
CREATE TABLE purchases (
    id INT PRIMARY KEY AUTO_INCREMENT,
    purchase_number VARCHAR(50) UNIQUE NOT NULL,
    supplier_id INT NOT NULL,
    employee_id INT NOT NULL,
    purchase_date DATE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    notes TEXT,
    status ENUM('pending', 'received', 'cancelled') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (employee_id) REFERENCES employees(id)
);

-- ===================================================
-- جدول تفاصيل المشتريات
-- Purchase Details Table
-- ===================================================
CREATE TABLE purchase_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    purchase_id INT NOT NULL,
    ingredient_id INT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_cost DECIMAL(10,3) NOT NULL,
    total_cost DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_id) REFERENCES purchases(id) ON DELETE CASCADE,
    FOREIGN KEY (ingredient_id) REFERENCES ingredients(id)
);

-- ===================================================
-- جدول الطاولات
-- Tables Table
-- ===================================================
CREATE TABLE restaurant_tables (
    id INT PRIMARY KEY AUTO_INCREMENT,
    table_number VARCHAR(10) NOT NULL UNIQUE,
    capacity INT NOT NULL,
    location VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===================================================
-- جدول العملاء
-- Customers Table
-- ===================================================
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- ===================================================
-- جدول الشيفتات
-- Shifts Table
-- ===================================================
CREATE TABLE shifts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    shift_number VARCHAR(50) UNIQUE NOT NULL,
    employee_id INT NOT NULL,
    start_time DATETIME NOT NULL,
    end_time DATETIME NULL,
    opening_cash DECIMAL(10,2) NOT NULL DEFAULT 0,
    closing_cash DECIMAL(10,2) NULL,
    expected_cash DECIMAL(10,2) NULL,
    cash_difference DECIMAL(10,2) NULL,
    total_sales DECIMAL(10,2) DEFAULT 0,
    total_expenses DECIMAL(10,2) DEFAULT 0,
    status ENUM('open', 'closed', 'suspended') DEFAULT 'open',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id)
);

-- ===================================================
-- جدول الطلبات
-- Orders Table
-- ===================================================
CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    order_type ENUM('dine_in', 'takeaway', 'delivery') NOT NULL,
    customer_id INT NULL,
    table_id INT NULL,
    delivery_address TEXT NULL,
    employee_id INT NOT NULL, -- الموظف المسؤول
    shift_id INT NOT NULL,
    order_date DATETIME NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'card', 'mixed') DEFAULT 'cash',
    status ENUM('new', 'preparing', 'ready', 'delivered', 'cancelled') DEFAULT 'new',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (table_id) REFERENCES restaurant_tables(id),
    FOREIGN KEY (employee_id) REFERENCES employees(id),
    FOREIGN KEY (shift_id) REFERENCES shifts(id)
);

-- ===================================================
-- جدول تفاصيل الطلبات
-- Order Details Table
-- ===================================================
CREATE TABLE order_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id INT NOT NULL,
    dish_id INT NOT NULL,
    quantity INT NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    special_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (dish_id) REFERENCES dishes(id)
);

-- ===================================================
-- جدول مصروفات الشيفت
-- Shift Expenses Table
-- ===================================================
CREATE TABLE shift_expenses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    shift_id INT NOT NULL,
    description VARCHAR(200) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    expense_date DATETIME NOT NULL,
    employee_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (shift_id) REFERENCES shifts(id),
    FOREIGN KEY (employee_id) REFERENCES employees(id)
);

-- ===================================================
-- جدول حركة المخزون
-- Stock Movements Table
-- ===================================================
CREATE TABLE stock_movements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ingredient_id INT NOT NULL,
    movement_type ENUM('in', 'out', 'adjustment') NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    reference_type ENUM('purchase', 'order', 'adjustment', 'waste') NOT NULL,
    reference_id INT NULL,
    notes TEXT,
    movement_date DATETIME NOT NULL,
    employee_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ingredient_id) REFERENCES ingredients(id),
    FOREIGN KEY (employee_id) REFERENCES employees(id)
);

-- ===================================================
-- جدول البرسنتاج
-- Employee Commissions Table
-- ===================================================
CREATE TABLE employee_commissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    order_id INT NOT NULL,
    commission_rate DECIMAL(5,2) NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL,
    commission_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id),
    FOREIGN KEY (order_id) REFERENCES orders(id),
    UNIQUE KEY unique_employee_order (employee_id, order_id)
);

-- ===================================================
-- جدول الحضور والانصراف
-- Employee Attendance Table
-- ===================================================
CREATE TABLE employee_attendance (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    check_in_time DATETIME NOT NULL,
    check_out_time DATETIME NULL,
    work_hours DECIMAL(4,2) NULL,
    attendance_date DATE NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id)
);

-- ===================================================
-- إدراج البيانات الأساسية
-- Insert Basic Data
-- ===================================================

-- إدراج الأدوار
INSERT INTO roles (name, name_fr, description) VALUES
('manager', 'Gérant', 'مدير المطعم - صلاحيات كاملة'),
('cashier', 'Caissier', 'كاشير - إدارة المبيعات والشيفتات'),
('cook', 'Cuisinier', 'طباخ - إدارة المطبخ وتحضير الطلبات'),
('waiter', 'Serveur', 'نادل - خدمة الطاولات وأخذ الطلبات');

-- إدراج وحدات القياس
INSERT INTO units (name, name_fr, abbreviation) VALUES
('كيلوغرام', 'Kilogramme', 'kg'),
('غرام', 'Gramme', 'g'),
('لتر', 'Litre', 'l'),
('مليلتر', 'Millilitre', 'ml'),
('قطعة', 'Pièce', 'pcs'),
('علبة', 'Boîte', 'box'),
('كيس', 'Sac', 'bag');

-- إدراج فئات الأطباق
INSERT INTO dish_categories (name, name_fr, description) VALUES
('بيتزا', 'Pizza', 'جميع أنواع البيتزا'),
('مقبلات', 'Entrées', 'المقبلات والسلطات'),
('مشروبات', 'Boissons', 'المشروبات الباردة والساخنة'),
('حلويات', 'Desserts', 'الحلويات والآيس كريم'),
('أطباق رئيسية', 'Plats principaux', 'الأطباق الرئيسية');

-- إدراج الطاولات
INSERT INTO restaurant_tables (table_number, capacity, location) VALUES
('T01', 2, 'نافذة'),
('T02', 4, 'وسط الصالة'),
('T03', 4, 'وسط الصالة'),
('T04', 6, 'زاوية'),
('T05', 2, 'نافذة'),
('T06', 8, 'صالة كبيرة');

-- ===================================================
-- إنشاء الفهارس لتحسين الأداء
-- Create Indexes for Performance
-- ===================================================
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_employee ON orders(employee_id);
CREATE INDEX idx_orders_shift ON orders(shift_id);
CREATE INDEX idx_stock_movements_date ON stock_movements(movement_date);
CREATE INDEX idx_stock_movements_ingredient ON stock_movements(ingredient_id);
CREATE INDEX idx_employee_commissions_date ON employee_commissions(commission_date);
CREATE INDEX idx_shifts_employee ON shifts(employee_id);
CREATE INDEX idx_shifts_date ON shifts(start_time);
