using System;
using System.Linq;
using System.Windows.Forms;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;

namespace PizzaRestoApp.UI.Management
{
    /// <summary>
    /// شاشة إدارة الأطباق
    /// Dishes Management Form
    /// </summary>
    public partial class DishesForm : Form
    {
        private readonly DishRepository _dishRepository;
        private readonly DishCategoryRepository _categoryRepository;
        private readonly DishIngredientRepository _dishIngredientRepository;
        private readonly IngredientRepository _ingredientRepository;

        public DishesForm()
        {
            InitializeComponent();
            _dishRepository = new DishRepository();
            _categoryRepository = new DishCategoryRepository();
            _dishIngredientRepository = new DishIngredientRepository();
            _ingredientRepository = new IngredientRepository();

            this.Text = "إدارة الأطباق - Dishes Management";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // تحميل الفئات في ComboBox
                var categories = await _categoryRepository.GetActiveCategoriesAsync();
                cmbCategory.DataSource = categories.ToList();
                cmbCategory.DisplayMember = "Name";
                cmbCategory.ValueMember = "Id";

                // تحميل المكونات
                var ingredients = await _ingredientRepository.GetActiveIngredientsAsync();
                cmbIngredient.DataSource = ingredients.ToList();
                cmbIngredient.DisplayMember = "Name";
                cmbIngredient.ValueMember = "Id";

                // تحميل الأطباق في DataGridView
                await LoadDishes();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadDishes()
        {
            var dishes = await _dishRepository.GetAllWithCategoriesAsync();
            dgvDishes.DataSource = dishes.ToList();

            // تخصيص أعمدة DataGridView
            if (dgvDishes.Columns.Count > 0)
            {
                dgvDishes.Columns["Id"].HeaderText = "المعرف";
                dgvDishes.Columns["Name"].HeaderText = "اسم الطبق";
                dgvDishes.Columns["NameFr"].HeaderText = "الاسم بالفرنسية";
                dgvDishes.Columns["Description"].HeaderText = "الوصف";
                dgvDishes.Columns["BaseCost"].HeaderText = "التكلفة الأساسية";
                dgvDishes.Columns["ProfitMargin"].HeaderText = "هامش الربح %";
                dgvDishes.Columns["SellingPrice"].HeaderText = "سعر البيع";
                dgvDishes.Columns["PreparationTime"].HeaderText = "وقت التحضير (دقيقة)";
                dgvDishes.Columns["IsAvailable"].HeaderText = "متاح";
                dgvDishes.Columns["IsActive"].HeaderText = "نشط";

                // إخفاء الأعمدة غير المطلوبة
                dgvDishes.Columns["CategoryId"].Visible = false;
                dgvDishes.Columns["CreatedAt"].Visible = false;
                dgvDishes.Columns["UpdatedAt"].Visible = false;
                dgvDishes.Columns["Category"].Visible = false;
                dgvDishes.Columns["Ingredients"].Visible = false;
            }
        }

        private async System.Threading.Tasks.Task LoadDishIngredients(int dishId)
        {
            var ingredients = await _dishRepository.GetDishIngredientsAsync(dishId);
            dgvDishIngredients.DataSource = ingredients.ToList();

            // تخصيص أعمدة مكونات الطبق
            if (dgvDishIngredients.Columns.Count > 0)
            {
                dgvDishIngredients.Columns["Id"].Visible = false;
                dgvDishIngredients.Columns["DishId"].Visible = false;
                dgvDishIngredients.Columns["IngredientId"].Visible = false;
                dgvDishIngredients.Columns["Quantity"].HeaderText = "الكمية";
                dgvDishIngredients.Columns["CreatedAt"].Visible = false;
                dgvDishIngredients.Columns["Dish"].Visible = false;
                dgvDishIngredients.Columns["Ingredient"].Visible = false;
            }
        }

        private void ClearForm()
        {
            txtName.Clear();
            txtNameFr.Clear();
            txtDescription.Clear();
            numBaseCost.Value = 0;
            numProfitMargin.Value = 30;
            numSellingPrice.Value = 0;
            numPreparationTime.Value = 15;
            chkIsAvailable.Checked = true;
            chkIsActive.Checked = true;
            cmbCategory.SelectedIndex = -1;

            dgvDishIngredients.DataSource = null;

            btnAdd.Enabled = true;
            btnUpdate.Enabled = false;
            btnDelete.Enabled = false;
            btnCalculateCost.Enabled = false;
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;

                var dish = new Dish
                {
                    Name = txtName.Text.Trim(),
                    NameFr = txtNameFr.Text.Trim(),
                    CategoryId = (int)cmbCategory.SelectedValue,
                    Description = txtDescription.Text.Trim(),
                    BaseCost = numBaseCost.Value,
                    ProfitMargin = numProfitMargin.Value,
                    SellingPrice = numSellingPrice.Value,
                    PreparationTime = (int)numPreparationTime.Value,
                    IsAvailable = chkIsAvailable.Checked,
                    IsActive = chkIsActive.Checked
                };

                await _dishRepository.AddAsync(dish);
                MessageBox.Show("تم إضافة الطبق بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                ClearForm();
                await LoadDishes();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الطبق: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;
                if (dgvDishes.CurrentRow == null) return;

                var dishId = (int)dgvDishes.CurrentRow.Cells["Id"].Value;
                var dish = await _dishRepository.GetByIdAsync(dishId);

                if (dish != null)
                {
                    dish.Name = txtName.Text.Trim();
                    dish.NameFr = txtNameFr.Text.Trim();
                    dish.CategoryId = (int)cmbCategory.SelectedValue;
                    dish.Description = txtDescription.Text.Trim();
                    dish.BaseCost = numBaseCost.Value;
                    dish.ProfitMargin = numProfitMargin.Value;
                    dish.SellingPrice = numSellingPrice.Value;
                    dish.PreparationTime = (int)numPreparationTime.Value;
                    dish.IsAvailable = chkIsAvailable.Checked;
                    dish.IsActive = chkIsActive.Checked;

                    await _dishRepository.UpdateAsync(dish);
                    MessageBox.Show("تم تحديث الطبق بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    ClearForm();
                    await LoadDishes();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الطبق: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvDishes.CurrentRow == null) return;

                if (MessageBox.Show("هل أنت متأكد من حذف هذا الطبق؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    var dishId = (int)dgvDishes.CurrentRow.Cells["Id"].Value;
                    await _dishRepository.DeleteAsync(dishId);

                    MessageBox.Show("تم حذف الطبق بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    ClearForm();
                    await LoadDishes();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الطبق: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void dgvDishes_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvDishes.CurrentRow != null)
            {
                var row = dgvDishes.CurrentRow;

                txtName.Text = row.Cells["Name"].Value?.ToString() ?? "";
                txtNameFr.Text = row.Cells["NameFr"].Value?.ToString() ?? "";
                txtDescription.Text = row.Cells["Description"].Value?.ToString() ?? "";
                numBaseCost.Value = Convert.ToDecimal(row.Cells["BaseCost"].Value ?? 0);
                numProfitMargin.Value = Convert.ToDecimal(row.Cells["ProfitMargin"].Value ?? 30);
                numSellingPrice.Value = Convert.ToDecimal(row.Cells["SellingPrice"].Value ?? 0);
                numPreparationTime.Value = Convert.ToDecimal(row.Cells["PreparationTime"].Value ?? 15);
                chkIsAvailable.Checked = Convert.ToBoolean(row.Cells["IsAvailable"].Value);
                chkIsActive.Checked = Convert.ToBoolean(row.Cells["IsActive"].Value);
                cmbCategory.SelectedValue = Convert.ToInt32(row.Cells["CategoryId"].Value);

                btnAdd.Enabled = false;
                btnUpdate.Enabled = true;
                btnDelete.Enabled = true;
                btnCalculateCost.Enabled = true;

                // تحميل مكونات الطبق
                var dishId = (int)row.Cells["Id"].Value;
                await LoadDishIngredients(dishId);
            }
        }

        private async void btnAddIngredient_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvDishes.CurrentRow == null)
                {
                    MessageBox.Show("يرجى اختيار طبق أولاً", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (cmbIngredient.SelectedValue == null || numQuantity.Value <= 0)
                {
                    MessageBox.Show("يرجى اختيار المكون وإدخال الكمية", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var dishId = (int)dgvDishes.CurrentRow.Cells["Id"].Value;
                var ingredientId = (int)cmbIngredient.SelectedValue;
                var quantity = numQuantity.Value;

                // التحقق من عدم وجود المكون مسبقاً
                if (await _dishIngredientRepository.ExistsAsync(dishId, ingredientId))
                {
                    MessageBox.Show("هذا المكون موجود بالفعل في الطبق", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var dishIngredient = new DishIngredient
                {
                    DishId = dishId,
                    IngredientId = ingredientId,
                    Quantity = quantity
                };

                await _dishIngredientRepository.AddAsync(dishIngredient);
                MessageBox.Show("تم إضافة المكون للطبق بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // إعادة تحميل مكونات الطبق
                await LoadDishIngredients(dishId);

                // مسح الحقول
                cmbIngredient.SelectedIndex = -1;
                numQuantity.Value = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المكون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnRemoveIngredient_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvDishIngredients.CurrentRow == null) return;

                if (MessageBox.Show("هل أنت متأكد من حذف هذا المكون من الطبق؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    var ingredientId = (int)dgvDishIngredients.CurrentRow.Cells["IngredientId"].Value;
                    var dishId = (int)dgvDishes.CurrentRow.Cells["Id"].Value;

                    await _dishIngredientRepository.RemoveIngredientAsync(dishId, ingredientId);

                    MessageBox.Show("تم حذف المكون من الطبق بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    await LoadDishIngredients(dishId);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المكون: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnCalculateCost_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvDishes.CurrentRow == null) return;

                var dishId = (int)dgvDishes.CurrentRow.Cells["Id"].Value;
                var calculatedCost = await _dishRepository.CalculateDishCostAsync(dishId);

                numBaseCost.Value = calculatedCost;

                // حساب سعر البيع تلقائياً
                var sellingPrice = calculatedCost * (1 + numProfitMargin.Value / 100);
                numSellingPrice.Value = sellingPrice;

                MessageBox.Show($"تم حساب التكلفة: {calculatedCost:C}\nسعر البيع المقترح: {sellingPrice:C}",
                    "حساب التكلفة", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حساب التكلفة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void numProfitMargin_ValueChanged(object sender, EventArgs e)
        {
            // حساب سعر البيع تلقائياً عند تغيير هامش الربح
            if (numBaseCost.Value > 0)
            {
                var sellingPrice = numBaseCost.Value * (1 + numProfitMargin.Value / 100);
                numSellingPrice.Value = sellingPrice;
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الطبق", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtNameFr.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم بالفرنسية", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNameFr.Focus();
                return false;
            }

            if (cmbCategory.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار الفئة", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbCategory.Focus();
                return false;
            }

            if (numSellingPrice.Value <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر البيع", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numSellingPrice.Focus();
                return false;
            }

            return true;
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
