using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.UI.Reports
{
    /// <summary>
    /// شاشة تقارير المبيعات
    /// Sales Reports Form
    /// </summary>
    public partial class SalesReportsForm : Form
    {
        private readonly OrderRepository _orderRepository;
        private readonly DishRepository _dishRepository;
        private readonly EmployeeRepository _employeeRepository;

        public SalesReportsForm()
        {
            InitializeComponent();
            _orderRepository = new OrderRepository();
            _dishRepository = new DishRepository();
            _employeeRepository = new EmployeeRepository();

            this.Text = "تقارير المبيعات - Sales Reports";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            InitializeReports();
        }

        private void InitializeReports()
        {
            // إعداد التواريخ الافتراضية
            dtpFromDate.Value = DateTime.Now.Date;
            dtpToDate.Value = DateTime.Now.Date;

            // إعداد أنواع التقارير
            cmbReportType.Items.Add(new { Text = "تقرير يومي", Value = "Daily" });
            cmbReportType.Items.Add(new { Text = "تقرير أسبوعي", Value = "Weekly" });
            cmbReportType.Items.Add(new { Text = "تقرير شهري", Value = "Monthly" });
            cmbReportType.Items.Add(new { Text = "تقرير سنوي", Value = "Yearly" });
            cmbReportType.Items.Add(new { Text = "تقرير مخصص", Value = "Custom" });
            cmbReportType.DisplayMember = "Text";
            cmbReportType.ValueMember = "Value";
            cmbReportType.SelectedIndex = 0;

            // إعداد Chart
            SetupChart();

            // تحميل التقرير الافتراضي
            LoadDailySalesReport();
        }

        private void SetupChart()
        {
            chartSales.Series.Clear();
            chartSales.ChartAreas.Clear();

            var chartArea = new ChartArea("SalesArea");
            chartArea.AxisX.Title = "التاريخ";
            chartArea.AxisY.Title = "المبيعات";
            chartArea.AxisX.LabelStyle.Angle = -45;
            chartSales.ChartAreas.Add(chartArea);

            var series = new Series("المبيعات");
            series.ChartType = SeriesChartType.Column;
            series.Color = Color.SteelBlue;
            chartSales.Series.Add(series);
        }

        private async void LoadDailySalesReport()
        {
            try
            {
                var fromDate = dtpFromDate.Value.Date;
                var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

                // تحميل بيانات المبيعات
                var orders = await _orderRepository.GetOrdersByDateRangeAsync(fromDate, toDate);
                var completedOrders = orders.Where(o => o.Status == OrderStatus.Completed).ToList();

                // إحصائيات عامة
                var totalSales = completedOrders.Sum(o => o.TotalAmount);
                var totalOrders = completedOrders.Count;
                var averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

                lblTotalSales.Text = $"إجمالي المبيعات: {totalSales:C}";
                lblTotalOrders.Text = $"عدد الطلبات: {totalOrders}";
                lblAverageOrder.Text = $"متوسط قيمة الطلب: {averageOrderValue:C}";

                // تحميل تفاصيل المبيعات
                var salesDetails = completedOrders.Select(o => new
                {
                    OrderNumber = o.OrderNumber,
                    OrderDate = o.OrderDate.ToString("yyyy-MM-dd HH:mm"),
                    CustomerName = o.Customer?.Name ?? "زبون مباشر",
                    OrderType = GetOrderTypeText(o.OrderType),
                    TotalAmount = o.TotalAmount,
                    PaymentMethod = GetPaymentMethodText(o.PaymentMethod),
                    Status = GetOrderStatusText(o.Status)
                }).ToList();

                dgvSalesReport.DataSource = salesDetails;

                // تحديث الرسم البياني
                UpdateChart(completedOrders);

                // تحميل أفضل الأطباق مبيعاً
                await LoadTopSellingDishes(fromDate, toDate);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير المبيعات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadTopSellingDishes(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var topDishes = await _orderRepository.GetTopSellingDishesAsync(fromDate, toDate, 10);

                var dishesData = topDishes.Select(d => new
                {
                    DishName = d.DishName,
                    QuantitySold = d.QuantitySold,
                    TotalRevenue = d.TotalRevenue,
                    Percentage = d.Percentage
                }).ToList();

                dgvTopDishes.DataSource = dishesData;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأطباق الأكثر مبيعاً: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateChart(List<Order> orders)
        {
            chartSales.Series["المبيعات"].Points.Clear();

            var salesByDate = orders
                .GroupBy(o => o.OrderDate.Date)
                .Select(g => new { Date = g.Key, Total = g.Sum(o => o.TotalAmount) })
                .OrderBy(x => x.Date)
                .ToList();

            foreach (var item in salesByDate)
            {
                chartSales.Series["المبيعات"].Points.AddXY(item.Date.ToString("MM/dd"), item.Total);
            }
        }

        private string GetOrderTypeText(OrderType orderType)
        {
            return orderType switch
            {
                OrderType.DineIn => "في المطعم",
                OrderType.TakeAway => "تيك أواي",
                OrderType.Delivery => "توصيل",
                _ => "غير محدد"
            };
        }

        private string GetPaymentMethodText(PaymentMethod paymentMethod)
        {
            return paymentMethod switch
            {
                PaymentMethod.Cash => "نقدي",
                PaymentMethod.Card => "بطاقة",
                PaymentMethod.Online => "أونلاين",
                _ => "غير محدد"
            };
        }

        private string GetOrderStatusText(OrderStatus status)
        {
            return status switch
            {
                OrderStatus.Pending => "معلق",
                OrderStatus.Confirmed => "مؤكد",
                OrderStatus.InProgress => "قيد التحضير",
                OrderStatus.Ready => "جاهز",
                OrderStatus.Completed => "مكتمل",
                OrderStatus.Cancelled => "ملغي",
                _ => "غير محدد"
            };
        }

        private async void btnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                var reportType = ((dynamic)cmbReportType.SelectedItem).Value.ToString();

                switch (reportType)
                {
                    case "Daily":
                        await LoadDailySalesReport();
                        break;
                    case "Weekly":
                        await LoadWeeklySalesReport();
                        break;
                    case "Monthly":
                        await LoadMonthlySalesReport();
                        break;
                    case "Yearly":
                        await LoadYearlySalesReport();
                        break;
                    case "Custom":
                        await LoadCustomSalesReport();
                        break;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadWeeklySalesReport()
        {
            var startOfWeek = dtpFromDate.Value.Date.AddDays(-(int)dtpFromDate.Value.DayOfWeek);
            var endOfWeek = startOfWeek.AddDays(7).AddSeconds(-1);

            dtpFromDate.Value = startOfWeek;
            dtpToDate.Value = endOfWeek.Date;

            await LoadSalesReportByDateRange(startOfWeek, endOfWeek);
        }

        private async System.Threading.Tasks.Task LoadMonthlySalesReport()
        {
            var startOfMonth = new DateTime(dtpFromDate.Value.Year, dtpFromDate.Value.Month, 1);
            var endOfMonth = startOfMonth.AddMonths(1).AddSeconds(-1);

            dtpFromDate.Value = startOfMonth;
            dtpToDate.Value = endOfMonth.Date;

            await LoadSalesReportByDateRange(startOfMonth, endOfMonth);
        }

        private async System.Threading.Tasks.Task LoadYearlySalesReport()
        {
            var startOfYear = new DateTime(dtpFromDate.Value.Year, 1, 1);
            var endOfYear = startOfYear.AddYears(1).AddSeconds(-1);

            dtpFromDate.Value = startOfYear;
            dtpToDate.Value = endOfYear.Date;

            await LoadSalesReportByDateRange(startOfYear, endOfYear);
        }

        private async System.Threading.Tasks.Task LoadCustomSalesReport()
        {
            var fromDate = dtpFromDate.Value.Date;
            var toDate = dtpToDate.Value.Date.AddDays(1).AddSeconds(-1);

            await LoadSalesReportByDateRange(fromDate, toDate);
        }

        private async System.Threading.Tasks.Task LoadSalesReportByDateRange(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var orders = await _orderRepository.GetOrdersByDateRangeAsync(fromDate, toDate);
                var completedOrders = orders.Where(o => o.Status == OrderStatus.Completed).ToList();

                // إحصائيات مفصلة
                var totalSales = completedOrders.Sum(o => o.TotalAmount);
                var totalOrders = completedOrders.Count;
                var averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;

                var dineInOrders = completedOrders.Where(o => o.OrderType == OrderType.DineIn).Sum(o => o.TotalAmount);
                var takeAwayOrders = completedOrders.Where(o => o.OrderType == OrderType.TakeAway).Sum(o => o.TotalAmount);
                var deliveryOrders = completedOrders.Where(o => o.OrderType == OrderType.Delivery).Sum(o => o.TotalAmount);

                var cashPayments = completedOrders.Where(o => o.PaymentMethod == PaymentMethod.Cash).Sum(o => o.TotalAmount);
                var cardPayments = completedOrders.Where(o => o.PaymentMethod == PaymentMethod.Card).Sum(o => o.TotalAmount);
                var onlinePayments = completedOrders.Where(o => o.PaymentMethod == PaymentMethod.Online).Sum(o => o.TotalAmount);

                // تحديث التسميات
                lblTotalSales.Text = $"إجمالي المبيعات: {totalSales:C}";
                lblTotalOrders.Text = $"عدد الطلبات: {totalOrders}";
                lblAverageOrder.Text = $"متوسط قيمة الطلب: {averageOrderValue:C}";
                lblDineIn.Text = $"في المطعم: {dineInOrders:C}";
                lblTakeAway.Text = $"تيك أواي: {takeAwayOrders:C}";
                lblDelivery.Text = $"توصيل: {deliveryOrders:C}";
                lblCash.Text = $"نقدي: {cashPayments:C}";
                lblCard.Text = $"بطاقة: {cardPayments:C}";
                lblOnline.Text = $"أونلاين: {onlinePayments:C}";

                // تحميل تفاصيل المبيعات
                var salesDetails = completedOrders.Select(o => new
                {
                    OrderNumber = o.OrderNumber,
                    OrderDate = o.OrderDate.ToString("yyyy-MM-dd HH:mm"),
                    CustomerName = o.Customer?.Name ?? "زبون مباشر",
                    OrderType = GetOrderTypeText(o.OrderType),
                    TotalAmount = o.TotalAmount,
                    PaymentMethod = GetPaymentMethodText(o.PaymentMethod),
                    Status = GetOrderStatusText(o.Status)
                }).ToList();

                dgvSalesReport.DataSource = salesDetails;

                // تحديث الرسم البياني
                UpdateChart(completedOrders);

                // تحميل أفضل الأطباق مبيعاً
                await LoadTopSellingDishes(fromDate, toDate);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تقرير المبيعات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnExportExcel_Click(object sender, EventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx",
                    FileName = $"SalesReport_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToExcel(saveDialog.FileName);
                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToExcel(string fileName)
        {
            // هنا يمكن إضافة كود تصدير Excel باستخدام مكتبة مثل EPPlus
            // سأضع كود أساسي للتوضيح

            using (var package = new OfficeOpenXml.ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("تقرير المبيعات");

                // إضافة العناوين
                worksheet.Cells[1, 1].Value = "رقم الطلب";
                worksheet.Cells[1, 2].Value = "التاريخ";
                worksheet.Cells[1, 3].Value = "اسم الزبون";
                worksheet.Cells[1, 4].Value = "نوع الطلب";
                worksheet.Cells[1, 5].Value = "المبلغ الإجمالي";
                worksheet.Cells[1, 6].Value = "طريقة الدفع";
                worksheet.Cells[1, 7].Value = "الحالة";

                // إضافة البيانات
                var data = (List<dynamic>)dgvSalesReport.DataSource;
                for (int i = 0; i < data.Count; i++)
                {
                    var row = data[i];
                    worksheet.Cells[i + 2, 1].Value = row.OrderNumber;
                    worksheet.Cells[i + 2, 2].Value = row.OrderDate;
                    worksheet.Cells[i + 2, 3].Value = row.CustomerName;
                    worksheet.Cells[i + 2, 4].Value = row.OrderType;
                    worksheet.Cells[i + 2, 5].Value = row.TotalAmount;
                    worksheet.Cells[i + 2, 6].Value = row.PaymentMethod;
                    worksheet.Cells[i + 2, 7].Value = row.Status;
                }

                // حفظ الملف
                package.SaveAs(new System.IO.FileInfo(fileName));
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    // هنا يمكن إضافة كود الطباعة
                    MessageBox.Show("تم إرسال التقرير للطباعة", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cmbReportType_SelectedIndexChanged(object sender, EventArgs e)
        {
            var reportType = ((dynamic)cmbReportType.SelectedItem).Value.ToString();

            // إظهار/إخفاء عناصر التحكم في التاريخ حسب نوع التقرير
            if (reportType == "Custom")
            {
                dtpFromDate.Enabled = true;
                dtpToDate.Enabled = true;
            }
            else
            {
                dtpFromDate.Enabled = false;
                dtpToDate.Enabled = false;
            }
        }
    }
}
