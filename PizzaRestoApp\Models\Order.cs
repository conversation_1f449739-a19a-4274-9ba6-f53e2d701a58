using System;
using System.Collections.Generic;
using System.Linq;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج الطلب
    /// Order Model
    /// </summary>
    public class Order
    {
        public int Id { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public OrderType OrderType { get; set; }
        public int? CustomerId { get; set; }
        public int? TableId { get; set; }
        public string? DeliveryAddress { get; set; }
        public int EmployeeId { get; set; }
        public int ShiftId { get; set; }
        public DateTime OrderDate { get; set; }
        public decimal Subtotal { get; set; }
        public decimal TaxAmount { get; set; } = 0;
        public decimal DiscountAmount { get; set; } = 0;
        public decimal TotalAmount { get; set; }
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;
        public OrderStatus Status { get; set; } = OrderStatus.New;
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public Customer? Customer { get; set; }
        public RestaurantTable? Table { get; set; }
        public Employee? Employee { get; set; }
        public Shift? Shift { get; set; }
        public List<OrderDetail> OrderDetails { get; set; } = new List<OrderDetail>();

        /// <summary>
        /// حساب المجموع الفرعي من تفاصيل الطلب
        /// Calculate subtotal from order details
        /// </summary>
        public decimal CalculateSubtotal()
        {
            return OrderDetails?.Sum(od => od.TotalPrice) ?? 0;
        }

        /// <summary>
        /// حساب المجموع الإجمالي
        /// Calculate total amount
        /// </summary>
        public decimal CalculateTotalAmount()
        {
            return Subtotal + TaxAmount - DiscountAmount;
        }

        /// <summary>
        /// تحديث المبالغ تلقائياً
        /// Update amounts automatically
        /// </summary>
        public void UpdateAmounts()
        {
            Subtotal = CalculateSubtotal();
            TotalAmount = CalculateTotalAmount();
        }

        /// <summary>
        /// التحقق من إمكانية تحضير الطلب (توفر المكونات)
        /// Check if order can be prepared (ingredients availability)
        /// </summary>
        public bool CanBePrepared()
        {
            return OrderDetails?.All(od => 
                od.Dish?.AreIngredientsAvailable() ?? false) ?? true;
        }

        /// <summary>
        /// تقدير وقت التحضير الإجمالي
        /// Estimate total preparation time
        /// </summary>
        public int EstimatedPreparationTime()
        {
            return OrderDetails?.Max(od => od.Dish?.PreparationTime ?? 0) ?? 0;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            bool isValid = !string.IsNullOrWhiteSpace(OrderNumber) &&
                          EmployeeId > 0 &&
                          ShiftId > 0 &&
                          OrderDate <= DateTime.Now &&
                          Subtotal >= 0 &&
                          TotalAmount >= 0;

            // التحقق من البيانات حسب نوع الطلب
            switch (OrderType)
            {
                case OrderType.DineIn:
                    isValid = isValid && TableId.HasValue;
                    break;
                case OrderType.Delivery:
                    isValid = isValid && !string.IsNullOrWhiteSpace(DeliveryAddress);
                    break;
            }

            return isValid;
        }

        /// <summary>
        /// تغيير حالة الطلب
        /// Change order status
        /// </summary>
        /// <param name="newStatus">الحالة الجديدة</param>
        public void ChangeStatus(OrderStatus newStatus)
        {
            Status = newStatus;
            UpdatedAt = DateTime.Now;
        }
    }
}
