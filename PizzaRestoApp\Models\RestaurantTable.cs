using System;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج طاولة المطعم
    /// Restaurant Table Model
    /// </summary>
    public class RestaurantTable
    {
        public int Id { get; set; }
        public string TableNumber { get; set; } = string.Empty;
        public int Capacity { get; set; }
        public string? Location { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(TableNumber) &&
                   Capacity > 0;
        }
    }
}
