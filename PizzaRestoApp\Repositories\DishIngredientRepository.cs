using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع مكونات الأطباق
    /// Dish Ingredient Repository
    /// </summary>
    public class DishIngredientRepository : BaseRepository<DishIngredient>
    {
        public DishIngredientRepository() : base("dish_ingredients") { }

        /// <summary>
        /// إضافة مكون للطبق
        /// Add ingredient to dish
        /// </summary>
        public override async Task<int> AddAsync(DishIngredient dishIngredient)
        {
            var sql = @"
                INSERT INTO dish_ingredients (dish_id, ingredient_id, quantity, created_at)
                VALUES (@DishId, @IngredientId, @Quantity, @CreatedAt);
                SELECT LAST_INSERT_ID();";

            dishIngredient.CreatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, dishIngredient);
        }

        /// <summary>
        /// تحديث مكون الطبق
        /// Update dish ingredient
        /// </summary>
        public override async Task<bool> UpdateAsync(DishIngredient dishIngredient)
        {
            var sql = @"
                UPDATE dish_ingredients 
                SET quantity = @Quantity
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, dishIngredient);
            return affectedRows > 0;
        }

        /// <summary>
        /// حذف جميع مكونات الطبق
        /// Delete all dish ingredients
        /// </summary>
        public async Task<bool> DeleteByDishIdAsync(int dishId)
        {
            var sql = "DELETE FROM dish_ingredients WHERE dish_id = @DishId";
            var affectedRows = await ExecuteAsync(sql, new { DishId = dishId });
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على مكونات الطبق
        /// Get dish ingredients
        /// </summary>
        public async Task<IEnumerable<DishIngredient>> GetByDishIdAsync(int dishId)
        {
            var sql = "SELECT * FROM dish_ingredients WHERE dish_id = @DishId";
            return await QueryAsync(sql, new { DishId = dishId });
        }

        /// <summary>
        /// التحقق من وجود مكون في الطبق
        /// Check if ingredient exists in dish
        /// </summary>
        public async Task<bool> ExistsAsync(int dishId, int ingredientId)
        {
            var sql = "SELECT COUNT(1) FROM dish_ingredients WHERE dish_id = @DishId AND ingredient_id = @IngredientId";
            var count = await QueryFirstOrDefaultAsync<int>(sql, new { DishId = dishId, IngredientId = ingredientId });
            return count > 0;
        }

        /// <summary>
        /// تحديث كمية المكون في الطبق
        /// Update ingredient quantity in dish
        /// </summary>
        public async Task<bool> UpdateQuantityAsync(int dishId, int ingredientId, decimal quantity)
        {
            var sql = @"
                UPDATE dish_ingredients 
                SET quantity = @Quantity
                WHERE dish_id = @DishId AND ingredient_id = @IngredientId";

            var affectedRows = await ExecuteAsync(sql, new 
            { 
                DishId = dishId, 
                IngredientId = ingredientId, 
                Quantity = quantity 
            });
            
            return affectedRows > 0;
        }

        /// <summary>
        /// حذف مكون من الطبق
        /// Remove ingredient from dish
        /// </summary>
        public async Task<bool> RemoveIngredientAsync(int dishId, int ingredientId)
        {
            var sql = "DELETE FROM dish_ingredients WHERE dish_id = @DishId AND ingredient_id = @IngredientId";
            var affectedRows = await ExecuteAsync(sql, new { DishId = dishId, IngredientId = ingredientId });
            return affectedRows > 0;
        }
    }
}
