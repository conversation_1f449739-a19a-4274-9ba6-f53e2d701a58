using System;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج وحدة القياس
    /// Unit of Measurement Model
    /// </summary>
    public class Unit
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string NameFr { get; set; } = string.Empty;
        public string Abbreviation { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   !string.IsNullOrWhiteSpace(NameFr) &&
                   !string.IsNullOrWhiteSpace(Abbreviation);
        }
    }
}
