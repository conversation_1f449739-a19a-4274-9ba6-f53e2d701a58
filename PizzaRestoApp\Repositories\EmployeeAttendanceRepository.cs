using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع حضور الموظفين
    /// Employee Attendance Repository
    /// </summary>
    public class EmployeeAttendanceRepository : BaseRepository<EmployeeAttendance>
    {
        public EmployeeAttendanceRepository() : base("employee_attendance") { }

        /// <summary>
        /// إضافة سجل حضور جديد
        /// Add new attendance record
        /// </summary>
        public override async Task<int> AddAsync(EmployeeAttendance attendance)
        {
            var sql = @"
                INSERT INTO employee_attendance (employee_id, check_in_time, check_out_time, 
                                                work_hours, attendance_date, notes, created_at, updated_at)
                VALUES (@EmployeeId, @CheckInTime, @CheckOutTime, 
                        @WorkHours, @AttendanceDate, @Notes, @CreatedAt, @UpdatedAt);
                SELECT LAST_INSERT_ID();";

            attendance.CreatedAt = DateTime.Now;
            attendance.UpdatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, attendance);
        }

        /// <summary>
        /// تحديث سجل حضور موجود
        /// Update existing attendance record
        /// </summary>
        public override async Task<bool> UpdateAsync(EmployeeAttendance attendance)
        {
            var sql = @"
                UPDATE employee_attendance 
                SET check_out_time = @CheckOutTime, work_hours = @WorkHours, 
                    notes = @Notes, updated_at = @UpdatedAt
                WHERE id = @Id";

            attendance.UpdatedAt = DateTime.Now;
            var affectedRows = await ExecuteAsync(sql, attendance);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على سجل الحضور للموظف في تاريخ معين
        /// Get attendance record for employee on specific date
        /// </summary>
        public async Task<EmployeeAttendance?> GetByEmployeeAndDateAsync(int employeeId, DateTime date)
        {
            var sql = @"
                SELECT ea.*, 
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM employee_attendance ea
                INNER JOIN employees e ON ea.employee_id = e.id
                WHERE ea.employee_id = @EmployeeId AND DATE(ea.attendance_date) = @Date";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<EmployeeAttendance, Employee, EmployeeAttendance>(
                sql,
                (attendance, employee) =>
                {
                    attendance.Employee = employee;
                    return attendance;
                },
                new { EmployeeId = employeeId, Date = date.Date },
                splitOn: "EmployeeFirstName"
            );

            return result.FirstOrDefault();
        }

        /// <summary>
        /// الحصول على سجلات الحضور للموظف حسب الفترة
        /// Get attendance records for employee by date range
        /// </summary>
        public async Task<IEnumerable<EmployeeAttendance>> GetByEmployeeAndDateRangeAsync(int employeeId, DateTime startDate, DateTime endDate)
        {
            var sql = @"
                SELECT ea.*, 
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM employee_attendance ea
                INNER JOIN employees e ON ea.employee_id = e.id
                WHERE ea.employee_id = @EmployeeId 
                  AND DATE(ea.attendance_date) BETWEEN @StartDate AND @EndDate
                ORDER BY ea.attendance_date DESC";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<EmployeeAttendance, Employee, EmployeeAttendance>(
                sql,
                (attendance, employee) =>
                {
                    attendance.Employee = employee;
                    return attendance;
                },
                new { EmployeeId = employeeId, StartDate = startDate.Date, EndDate = endDate.Date },
                splitOn: "EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على جميع سجلات الحضور في تاريخ معين
        /// Get all attendance records on specific date
        /// </summary>
        public async Task<IEnumerable<EmployeeAttendance>> GetByDateAsync(DateTime date)
        {
            var sql = @"
                SELECT ea.*, 
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM employee_attendance ea
                INNER JOIN employees e ON ea.employee_id = e.id
                WHERE DATE(ea.attendance_date) = @Date
                ORDER BY e.first_name, e.last_name";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<EmployeeAttendance, Employee, EmployeeAttendance>(
                sql,
                (attendance, employee) =>
                {
                    attendance.Employee = employee;
                    return attendance;
                },
                new { Date = date.Date },
                splitOn: "EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// تسجيل دخول الموظف
        /// Employee check-in
        /// </summary>
        public async Task<int> CheckInAsync(int employeeId, string? notes = null)
        {
            var now = DateTime.Now;
            var attendance = new EmployeeAttendance
            {
                EmployeeId = employeeId,
                CheckInTime = now,
                AttendanceDate = now.Date,
                Notes = notes
            };

            return await AddAsync(attendance);
        }

        /// <summary>
        /// تسجيل خروج الموظف
        /// Employee check-out
        /// </summary>
        public async Task<bool> CheckOutAsync(int employeeId, string? notes = null)
        {
            var today = DateTime.Now.Date;
            var attendance = await GetByEmployeeAndDateAsync(employeeId, today);
            
            if (attendance != null && !attendance.CheckOutTime.HasValue)
            {
                attendance.CheckOut();
                if (!string.IsNullOrWhiteSpace(notes))
                {
                    attendance.Notes = string.IsNullOrWhiteSpace(attendance.Notes) ? notes : $"{attendance.Notes}; {notes}";
                }
                
                return await UpdateAsync(attendance);
            }

            return false;
        }

        /// <summary>
        /// الحصول على إجمالي ساعات العمل للموظف
        /// Get total work hours for employee
        /// </summary>
        public async Task<decimal> GetTotalWorkHoursAsync(int employeeId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var sql = @"
                SELECT COALESCE(SUM(work_hours), 0)
                FROM employee_attendance
                WHERE employee_id = @EmployeeId AND work_hours IS NOT NULL";

            var parameters = new { EmployeeId = employeeId };

            if (startDate.HasValue && endDate.HasValue)
            {
                sql += " AND DATE(attendance_date) BETWEEN @StartDate AND @EndDate";
                parameters = new { EmployeeId = employeeId, StartDate = startDate.Value.Date, EndDate = endDate.Value.Date };
            }

            return await QueryFirstOrDefaultAsync<decimal>(sql, parameters);
        }

        /// <summary>
        /// الحصول على الموظفين الحاضرين حالياً
        /// Get currently present employees
        /// </summary>
        public async Task<IEnumerable<EmployeeAttendance>> GetCurrentlyPresentAsync()
        {
            var today = DateTime.Now.Date;
            var sql = @"
                SELECT ea.*, 
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName
                FROM employee_attendance ea
                INNER JOIN employees e ON ea.employee_id = e.id
                WHERE DATE(ea.attendance_date) = @Today 
                  AND ea.check_in_time IS NOT NULL 
                  AND ea.check_out_time IS NULL
                ORDER BY e.first_name, e.last_name";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<EmployeeAttendance, Employee, EmployeeAttendance>(
                sql,
                (attendance, employee) =>
                {
                    attendance.Employee = employee;
                    return attendance;
                },
                new { Today = today },
                splitOn: "EmployeeFirstName"
            );

            return result;
        }
    }
}
