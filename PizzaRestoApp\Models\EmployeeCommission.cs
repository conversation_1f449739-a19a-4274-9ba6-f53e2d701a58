using System;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج عمولة الموظف (البرسنتاج)
    /// Employee Commission Model
    /// </summary>
    public class EmployeeCommission
    {
        public int Id { get; set; }
        public int EmployeeId { get; set; }
        public int OrderId { get; set; }
        public decimal CommissionRate { get; set; }
        public decimal CommissionAmount { get; set; }
        public DateTime CommissionDate { get; set; }
        public DateTime CreatedAt { get; set; }

        // Navigation Properties
        public Employee? Employee { get; set; }
        public Order? Order { get; set; }

        /// <summary>
        /// حساب مبلغ العمولة
        /// Calculate commission amount
        /// </summary>
        /// <param name="orderTotal">إجمالي الطلب</param>
        public decimal CalculateCommissionAmount(decimal orderTotal)
        {
            return orderTotal * (CommissionRate / 100);
        }

        /// <summary>
        /// تحديث مبلغ العمولة
        /// Update commission amount
        /// </summary>
        /// <param name="orderTotal">إجمالي الطلب</param>
        public void UpdateCommissionAmount(decimal orderTotal)
        {
            CommissionAmount = CalculateCommissionAmount(orderTotal);
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            return EmployeeId > 0 &&
                   OrderId > 0 &&
                   CommissionRate >= 0 &&
                   CommissionAmount >= 0 &&
                   CommissionDate <= DateTime.Now;
        }
    }
}
