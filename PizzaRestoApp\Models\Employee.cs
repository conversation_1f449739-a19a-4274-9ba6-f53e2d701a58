using System;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج الموظف
    /// Employee Model
    /// </summary>
    public class Employee
    {
        public int Id { get; set; }
        public string EmployeeCode { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public int RoleId { get; set; }
        public DateTime HireDate { get; set; }
        public decimal? Salary { get; set; }
        public decimal PercentageRate { get; set; } = 0.0m;
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public Role? Role { get; set; }

        /// <summary>
        /// الاسم الكامل
        /// Full Name
        /// </summary>
        public string FullName => $"{FirstName} {LastName}";

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(EmployeeCode) &&
                   !string.IsNullOrWhiteSpace(FirstName) &&
                   !string.IsNullOrWhiteSpace(LastName) &&
                   RoleId > 0 &&
                   HireDate <= DateTime.Now;
        }
    }
}
