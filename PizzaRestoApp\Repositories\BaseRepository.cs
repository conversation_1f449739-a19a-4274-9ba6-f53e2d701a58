using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Dapper;
using MySql.Data.MySqlClient;
using PizzaRestoApp.Config;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// المستودع الأساسي
    /// Base Repository
    /// </summary>
    /// <typeparam name="T">نوع الكائن</typeparam>
    public abstract class BaseRepository<T> : IRepository<T> where T : class
    {
        protected readonly string _connectionString;
        protected readonly string _tableName;

        protected BaseRepository(string tableName)
        {
            _connectionString = DatabaseConfig.ConnectionString;
            _tableName = tableName;
        }

        /// <summary>
        /// إنشاء اتصال بقاعدة البيانات
        /// Create database connection
        /// </summary>
        protected IDbConnection CreateConnection()
        {
            return new MySqlConnection(_connectionString);
        }

        /// <summary>
        /// الحصول على جميع السجلات
        /// Get all records
        /// </summary>
        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            using var connection = CreateConnection();
            var sql = $"SELECT * FROM {_tableName}";
            return await connection.QueryAsync<T>(sql);
        }

        /// <summary>
        /// الحصول على سجل بالمعرف
        /// Get record by ID
        /// </summary>
        public virtual async Task<T?> GetByIdAsync(int id)
        {
            using var connection = CreateConnection();
            var sql = $"SELECT * FROM {_tableName} WHERE id = @Id";
            return await connection.QueryFirstOrDefaultAsync<T>(sql, new { Id = id });
        }

        /// <summary>
        /// إضافة سجل جديد
        /// Add new record
        /// </summary>
        public abstract Task<int> AddAsync(T entity);

        /// <summary>
        /// تحديث سجل موجود
        /// Update existing record
        /// </summary>
        public abstract Task<bool> UpdateAsync(T entity);

        /// <summary>
        /// حذف سجل
        /// Delete record
        /// </summary>
        public virtual async Task<bool> DeleteAsync(int id)
        {
            using var connection = CreateConnection();
            var sql = $"DELETE FROM {_tableName} WHERE id = @Id";
            var affectedRows = await connection.ExecuteAsync(sql, new { Id = id });
            return affectedRows > 0;
        }

        /// <summary>
        /// التحقق من وجود سجل
        /// Check if record exists
        /// </summary>
        public virtual async Task<bool> ExistsAsync(int id)
        {
            using var connection = CreateConnection();
            var sql = $"SELECT COUNT(1) FROM {_tableName} WHERE id = @Id";
            var count = await connection.QuerySingleAsync<int>(sql, new { Id = id });
            return count > 0;
        }

        /// <summary>
        /// عدد السجلات
        /// Count records
        /// </summary>
        public virtual async Task<int> CountAsync()
        {
            using var connection = CreateConnection();
            var sql = $"SELECT COUNT(*) FROM {_tableName}";
            return await connection.QuerySingleAsync<int>(sql);
        }

        /// <summary>
        /// تنفيذ استعلام مخصص
        /// Execute custom query
        /// </summary>
        protected async Task<IEnumerable<T>> QueryAsync(string sql, object? parameters = null)
        {
            using var connection = CreateConnection();
            return await connection.QueryAsync<T>(sql, parameters);
        }

        /// <summary>
        /// تنفيذ استعلام مخصص للحصول على سجل واحد
        /// Execute custom query for single record
        /// </summary>
        protected async Task<T?> QueryFirstOrDefaultAsync(string sql, object? parameters = null)
        {
            using var connection = CreateConnection();
            return await connection.QueryFirstOrDefaultAsync<T>(sql, parameters);
        }

        /// <summary>
        /// تنفيذ أمر مخصص
        /// Execute custom command
        /// </summary>
        protected async Task<int> ExecuteAsync(string sql, object? parameters = null)
        {
            using var connection = CreateConnection();
            return await connection.ExecuteAsync(sql, parameters);
        }
    }
}
