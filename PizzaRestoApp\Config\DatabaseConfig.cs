using System;
using System.Configuration;

namespace PizzaRestoApp.Config
{
    /// <summary>
    /// إعدادات قاعدة البيانات
    /// Database Configuration
    /// </summary>
    public static class DatabaseConfig
    {
        /// <summary>
        /// سلسلة الاتصال بقاعدة البيانات
        /// Database Connection String
        /// </summary>
        public static string ConnectionString { get; private set; }

        /// <summary>
        /// تهيئة إعدادات قاعدة البيانات
        /// Initialize Database Configuration
        /// </summary>
        static DatabaseConfig()
        {
            // يمكن قراءة هذه القيم من ملف التكوين أو متغيرات البيئة
            // These values can be read from config file or environment variables
            string server = "localhost";
            string database = "pizzaresto_db";
            string username = "root";
            string password = "";
            int port = 3306;

            ConnectionString = $"Server={server};Port={port};Database={database};Uid={username};Pwd={password};CharSet=utf8mb4;";
        }

        /// <summary>
        /// تحديث سلسلة الاتصال
        /// Update Connection String
        /// </summary>
        /// <param name="server">خادم قاعدة البيانات</param>
        /// <param name="database">اسم قاعدة البيانات</param>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <param name="port">رقم المنفذ</param>
        public static void UpdateConnectionString(string server, string database, string username, string password, int port = 3306)
        {
            ConnectionString = $"Server={server};Port={port};Database={database};Uid={username};Pwd={password};CharSet=utf8mb4;";
        }
    }
}
