using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PizzaRestoApp.Models;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع الشيفتات
    /// Shift Repository
    /// </summary>
    public class ShiftRepository : BaseRepository<Shift>
    {
        public ShiftRepository() : base("shifts") { }

        /// <summary>
        /// إضافة شيفت جديد
        /// Add new shift
        /// </summary>
        public override async Task<int> AddAsync(Shift shift)
        {
            var sql = @"
                INSERT INTO shifts (shift_number, employee_id, start_time, end_time, opening_cash, 
                                  closing_cash, expected_cash, cash_difference, total_sales, 
                                  total_expenses, status, notes, created_at, updated_at)
                VALUES (@ShiftNumber, @EmployeeId, @StartTime, @EndTime, @OpeningCash, 
                        @ClosingCash, @ExpectedCash, @CashDifference, @TotalSales, 
                        @TotalExpenses, @Status, @Notes, @CreatedAt, @UpdatedAt);
                SELECT LAST_INSERT_ID();";

            shift.CreatedAt = DateTime.Now;
            shift.UpdatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, shift);
        }

        /// <summary>
        /// تحديث شيفت موجود
        /// Update existing shift
        /// </summary>
        public override async Task<bool> UpdateAsync(Shift shift)
        {
            var sql = @"
                UPDATE shifts 
                SET shift_number = @ShiftNumber, employee_id = @EmployeeId, start_time = @StartTime,
                    end_time = @EndTime, opening_cash = @OpeningCash, closing_cash = @ClosingCash,
                    expected_cash = @ExpectedCash, cash_difference = @CashDifference, 
                    total_sales = @TotalSales, total_expenses = @TotalExpenses, 
                    status = @Status, notes = @Notes, updated_at = @UpdatedAt
                WHERE id = @Id";

            shift.UpdatedAt = DateTime.Now;
            var affectedRows = await ExecuteAsync(sql, shift);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على شيفت مع تفاصيل الموظف
        /// Get shift with employee details
        /// </summary>
        public async Task<Shift?> GetByIdWithEmployeeAsync(int id)
        {
            var sql = @"
                SELECT s.*, e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName,
                       e.employee_code as EmployeeCode
                FROM shifts s
                INNER JOIN employees e ON s.employee_id = e.id
                WHERE s.id = @Id";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Shift, Employee, Shift>(
                sql,
                (shift, employee) =>
                {
                    shift.Employee = employee;
                    return shift;
                },
                new { Id = id },
                splitOn: "EmployeeFirstName"
            );

            return result.FirstOrDefault();
        }

        /// <summary>
        /// الحصول على الشيفت المفتوح للموظف
        /// Get open shift for employee
        /// </summary>
        public async Task<Shift?> GetOpenShiftByEmployeeAsync(int employeeId)
        {
            var sql = @"
                SELECT s.*, e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName,
                       e.employee_code as EmployeeCode
                FROM shifts s
                INNER JOIN employees e ON s.employee_id = e.id
                WHERE s.employee_id = @EmployeeId AND s.status = @Status";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Shift, Employee, Shift>(
                sql,
                (shift, employee) =>
                {
                    shift.Employee = employee;
                    return shift;
                },
                new { EmployeeId = employeeId, Status = ShiftStatus.Open },
                splitOn: "EmployeeFirstName"
            );

            return result.FirstOrDefault();
        }

        /// <summary>
        /// الحصول على جميع الشيفتات مع تفاصيل الموظفين
        /// Get all shifts with employee details
        /// </summary>
        public async Task<IEnumerable<Shift>> GetAllWithEmployeesAsync()
        {
            var sql = @"
                SELECT s.*, e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName,
                       e.employee_code as EmployeeCode
                FROM shifts s
                INNER JOIN employees e ON s.employee_id = e.id
                ORDER BY s.start_time DESC";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Shift, Employee, Shift>(
                sql,
                (shift, employee) =>
                {
                    shift.Employee = employee;
                    return shift;
                },
                splitOn: "EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على الشيفتات حسب التاريخ
        /// Get shifts by date
        /// </summary>
        public async Task<IEnumerable<Shift>> GetByDateAsync(DateTime date)
        {
            var sql = @"
                SELECT s.*, e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName,
                       e.employee_code as EmployeeCode
                FROM shifts s
                INNER JOIN employees e ON s.employee_id = e.id
                WHERE DATE(s.start_time) = @Date
                ORDER BY s.start_time";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Shift, Employee, Shift>(
                sql,
                (shift, employee) =>
                {
                    shift.Employee = employee;
                    return shift;
                },
                new { Date = date.Date },
                splitOn: "EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على الشيفتات حسب الموظف والفترة
        /// Get shifts by employee and date range
        /// </summary>
        public async Task<IEnumerable<Shift>> GetByEmployeeAndDateRangeAsync(int employeeId, DateTime startDate, DateTime endDate)
        {
            var sql = @"
                SELECT s.*, e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName,
                       e.employee_code as EmployeeCode
                FROM shifts s
                INNER JOIN employees e ON s.employee_id = e.id
                WHERE s.employee_id = @EmployeeId 
                  AND DATE(s.start_time) BETWEEN @StartDate AND @EndDate
                ORDER BY s.start_time DESC";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Shift, Employee, Shift>(
                sql,
                (shift, employee) =>
                {
                    shift.Employee = employee;
                    return shift;
                },
                new { EmployeeId = employeeId, StartDate = startDate.Date, EndDate = endDate.Date },
                splitOn: "EmployeeFirstName"
            );

            return result;
        }

        /// <summary>
        /// تحديث مبيعات الشيفت
        /// Update shift sales
        /// </summary>
        public async Task<bool> UpdateSalesAsync(int shiftId, decimal totalSales)
        {
            var sql = @"
                UPDATE shifts 
                SET total_sales = @TotalSales, updated_at = @UpdatedAt
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, new 
            { 
                Id = shiftId, 
                TotalSales = totalSales, 
                UpdatedAt = DateTime.Now 
            });
            
            return affectedRows > 0;
        }

        /// <summary>
        /// تحديث مصروفات الشيفت
        /// Update shift expenses
        /// </summary>
        public async Task<bool> UpdateExpensesAsync(int shiftId, decimal totalExpenses)
        {
            var sql = @"
                UPDATE shifts 
                SET total_expenses = @TotalExpenses, updated_at = @UpdatedAt
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, new 
            { 
                Id = shiftId, 
                TotalExpenses = totalExpenses, 
                UpdatedAt = DateTime.Now 
            });
            
            return affectedRows > 0;
        }

        /// <summary>
        /// إغلاق الشيفت
        /// Close shift
        /// </summary>
        public async Task<bool> CloseShiftAsync(int shiftId, decimal closingCash, string? notes = null)
        {
            var sql = @"
                UPDATE shifts 
                SET end_time = @EndTime, closing_cash = @ClosingCash, 
                    expected_cash = opening_cash + total_sales - total_expenses,
                    cash_difference = @ClosingCash - (opening_cash + total_sales - total_expenses),
                    status = @Status, notes = @Notes, updated_at = @UpdatedAt
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, new 
            { 
                Id = shiftId,
                EndTime = DateTime.Now,
                ClosingCash = closingCash,
                Status = ShiftStatus.Closed,
                Notes = notes,
                UpdatedAt = DateTime.Now 
            });
            
            return affectedRows > 0;
        }
    }
}
