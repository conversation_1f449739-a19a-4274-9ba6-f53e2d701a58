using System;
using System.Windows.Forms;

namespace PizzaRestoApp.UI.Management
{
    /// <summary>
    /// شاشة الإدارة الرئيسية
    /// Management Main Form
    /// </summary>
    public partial class ManagementMainForm : Form
    {
        public ManagementMainForm()
        {
            InitializeComponent();
            this.Text = "الإدارة - Management";
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void btnEmployees_Click(object sender, EventArgs e)
        {
            var employeesForm = new EmployeesForm();
            employeesForm.ShowDialog();
        }

        private void btnDishes_Click(object sender, EventArgs e)
        {
            var dishesForm = new DishesForm();
            dishesForm.ShowDialog();
        }

        private void btnIngredients_Click(object sender, EventArgs e)
        {
            var ingredientsForm = new IngredientsForm();
            ingredientsForm.ShowDialog();
        }

        private void btnSuppliers_Click(object sender, EventArgs e)
        {
            var suppliersForm = new SuppliersForm();
            suppliersForm.ShowDialog();
        }

        private void btnPurchases_Click(object sender, EventArgs e)
        {
            var purchasesForm = new PurchasesForm();
            purchasesForm.ShowDialog();
        }

        private void btnTables_Click(object sender, EventArgs e)
        {
            var tablesForm = new TablesForm();
            tablesForm.ShowDialog();
        }

        private void btnSettings_Click(object sender, EventArgs e)
        {
            var settingsForm = new SettingsForm();
            settingsForm.ShowDialog();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
