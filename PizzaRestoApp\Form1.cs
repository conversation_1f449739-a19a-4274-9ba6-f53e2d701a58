using PizzaRestoApp.UI.Management;
using PizzaRestoApp.UI.Kitchen;
using PizzaRestoApp.UI.POS;
using PizzaRestoApp.UI.Reports;

namespace PizzaRestoApp;

/// <summary>
/// الشاشة الرئيسية للبرنامج
/// Main Application Form
/// </summary>
public partial class MainForm : Form
{
    public MainForm()
    {
        InitializeComponent();
        this.Text = "نظام إدارة مطعم وبيزيريا - Pizza Restaurant Management System";
        this.WindowState = FormWindowState.Maximized;
        this.RightToLeft = RightToLeft.Yes;
        this.RightToLeftLayout = true;
    }

    private void btnManagement_Click(object sender, EventArgs e)
    {
        var managementForm = new ManagementMainForm();
        managementForm.ShowDialog();
    }

    private void btnKitchen_Click(object sender, EventArgs e)
    {
        var kitchenForm = new KitchenDisplayForm();
        kitchenForm.ShowDialog();
    }

    private void btnPOS_Click(object sender, EventArgs e)
    {
        var posForm = new POSMainForm();
        posForm.ShowDialog();
    }

    private void btnReports_Click(object sender, EventArgs e)
    {
        var reportsForm = new ReportsMainForm();
        reportsForm.ShowDialog();
    }

    private void btnExit_Click(object sender, EventArgs e)
    {
        if (MessageBox.Show("هل تريد إغلاق البرنامج؟", "تأكيد الخروج",
            MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
        {
            Application.Exit();
        }
    }
}
