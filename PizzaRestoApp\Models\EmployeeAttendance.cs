using System;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج حضور الموظف
    /// Employee Attendance Model
    /// </summary>
    public class EmployeeAttendance
    {
        public int Id { get; set; }
        public int EmployeeId { get; set; }
        public DateTime CheckInTime { get; set; }
        public DateTime? CheckOutTime { get; set; }
        public decimal? WorkHours { get; set; }
        public DateTime AttendanceDate { get; set; }
        public string? Notes { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }

        // Navigation Properties
        public Employee? Employee { get; set; }

        /// <summary>
        /// حساب ساعات العمل
        /// Calculate work hours
        /// </summary>
        public decimal? CalculateWorkHours()
        {
            if (CheckOutTime.HasValue)
            {
                var duration = CheckOutTime.Value - CheckInTime;
                return (decimal)duration.TotalHours;
            }
            return null;
        }

        /// <summary>
        /// تحديث ساعات العمل
        /// Update work hours
        /// </summary>
        public void UpdateWorkHours()
        {
            WorkHours = CalculateWorkHours();
        }

        /// <summary>
        /// تسجيل الخروج
        /// Check out
        /// </summary>
        public void CheckOut()
        {
            CheckOutTime = DateTime.Now;
            UpdateWorkHours();
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            return EmployeeId > 0 &&
                   CheckInTime <= DateTime.Now &&
                   AttendanceDate.Date == CheckInTime.Date;
        }
    }
}
