using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع المكونات
    /// Ingredient Repository
    /// </summary>
    public class IngredientRepository : BaseRepository<Ingredient>
    {
        public IngredientRepository() : base("ingredients") { }

        /// <summary>
        /// إضافة مكون جديد
        /// Add new ingredient
        /// </summary>
        public override async Task<int> AddAsync(Ingredient ingredient)
        {
            var sql = @"
                INSERT INTO ingredients (name, name_fr, unit_id, cost_per_unit, current_stock, 
                                       min_stock_alert, is_active, created_at, updated_at)
                VALUES (@Name, @NameFr, @UnitId, @CostPerUnit, @CurrentStock, 
                        @MinStockAlert, @IsActive, @CreatedAt, @UpdatedAt);
                SELECT LAST_INSERT_ID();";

            ingredient.CreatedAt = DateTime.Now;
            ingredient.UpdatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, ingredient);
        }

        /// <summary>
        /// تحديث مكون موجود
        /// Update existing ingredient
        /// </summary>
        public override async Task<bool> UpdateAsync(Ingredient ingredient)
        {
            var sql = @"
                UPDATE ingredients 
                SET name = @Name, name_fr = @NameFr, unit_id = @UnitId, 
                    cost_per_unit = @CostPerUnit, current_stock = @CurrentStock,
                    min_stock_alert = @MinStockAlert, is_active = @IsActive, 
                    updated_at = @UpdatedAt
                WHERE id = @Id";

            ingredient.UpdatedAt = DateTime.Now;
            var affectedRows = await ExecuteAsync(sql, ingredient);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على مكون مع تفاصيل الوحدة
        /// Get ingredient with unit details
        /// </summary>
        public async Task<Ingredient?> GetByIdWithUnitAsync(int id)
        {
            var sql = @"
                SELECT i.*, u.name as UnitName, u.name_fr as UnitNameFr, u.abbreviation as UnitAbbreviation
                FROM ingredients i
                INNER JOIN units u ON i.unit_id = u.id
                WHERE i.id = @Id";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Ingredient, Unit, Ingredient>(
                sql,
                (ingredient, unit) =>
                {
                    ingredient.Unit = unit;
                    return ingredient;
                },
                new { Id = id },
                splitOn: "UnitName"
            );

            return result.FirstOrDefault();
        }

        /// <summary>
        /// الحصول على جميع المكونات مع تفاصيل الوحدات
        /// Get all ingredients with unit details
        /// </summary>
        public async Task<IEnumerable<Ingredient>> GetAllWithUnitsAsync()
        {
            var sql = @"
                SELECT i.*, u.name as UnitName, u.name_fr as UnitNameFr, u.abbreviation as UnitAbbreviation
                FROM ingredients i
                INNER JOIN units u ON i.unit_id = u.id
                ORDER BY i.name";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Ingredient, Unit, Ingredient>(
                sql,
                (ingredient, unit) =>
                {
                    ingredient.Unit = unit;
                    return ingredient;
                },
                splitOn: "UnitName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على المكونات النشطة
        /// Get active ingredients
        /// </summary>
        public async Task<IEnumerable<Ingredient>> GetActiveIngredientsAsync()
        {
            var sql = @"
                SELECT i.*, u.name as UnitName, u.name_fr as UnitNameFr, u.abbreviation as UnitAbbreviation
                FROM ingredients i
                INNER JOIN units u ON i.unit_id = u.id
                WHERE i.is_active = 1
                ORDER BY i.name";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Ingredient, Unit, Ingredient>(
                sql,
                (ingredient, unit) =>
                {
                    ingredient.Unit = unit;
                    return ingredient;
                },
                splitOn: "UnitName"
            );

            return result;
        }

        /// <summary>
        /// الحصول على المكونات منخفضة المخزون
        /// Get low stock ingredients
        /// </summary>
        public async Task<IEnumerable<Ingredient>> GetLowStockIngredientsAsync()
        {
            var sql = @"
                SELECT i.*, u.name as UnitName, u.name_fr as UnitNameFr, u.abbreviation as UnitAbbreviation
                FROM ingredients i
                INNER JOIN units u ON i.unit_id = u.id
                WHERE i.is_active = 1 AND i.current_stock <= i.min_stock_alert
                ORDER BY i.current_stock";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Ingredient, Unit, Ingredient>(
                sql,
                (ingredient, unit) =>
                {
                    ingredient.Unit = unit;
                    return ingredient;
                },
                splitOn: "UnitName"
            );

            return result;
        }

        /// <summary>
        /// تحديث مخزون المكون
        /// Update ingredient stock
        /// </summary>
        public async Task<bool> UpdateStockAsync(int ingredientId, decimal newStock)
        {
            var sql = @"
                UPDATE ingredients 
                SET current_stock = @NewStock, updated_at = @UpdatedAt
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, new 
            { 
                Id = ingredientId, 
                NewStock = newStock, 
                UpdatedAt = DateTime.Now 
            });
            
            return affectedRows > 0;
        }

        /// <summary>
        /// البحث في المكونات
        /// Search ingredients
        /// </summary>
        public async Task<IEnumerable<Ingredient>> SearchAsync(string searchTerm)
        {
            var sql = @"
                SELECT i.*, u.name as UnitName, u.name_fr as UnitNameFr, u.abbreviation as UnitAbbreviation
                FROM ingredients i
                INNER JOIN units u ON i.unit_id = u.id
                WHERE i.is_active = 1 AND (i.name LIKE @SearchTerm OR i.name_fr LIKE @SearchTerm)
                ORDER BY i.name";

            var searchPattern = $"%{searchTerm}%";
            
            using var connection = CreateConnection();
            var result = await connection.QueryAsync<Ingredient, Unit, Ingredient>(
                sql,
                (ingredient, unit) =>
                {
                    ingredient.Unit = unit;
                    return ingredient;
                },
                new { SearchTerm = searchPattern },
                splitOn: "UnitName"
            );

            return result;
        }
    }
}
