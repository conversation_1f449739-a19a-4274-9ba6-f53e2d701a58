using System;

namespace PizzaRestoApp.Models
{
    /// <summary>
    /// نموذج مصروفات الشيفت
    /// Shift Expense Model
    /// </summary>
    public class ShiftExpense
    {
        public int Id { get; set; }
        public int ShiftId { get; set; }
        public string Description { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public DateTime ExpenseDate { get; set; }
        public int EmployeeId { get; set; }
        public DateTime CreatedAt { get; set; }

        // Navigation Properties
        public Shift? Shift { get; set; }
        public Employee? Employee { get; set; }

        /// <summary>
        /// التحقق من صحة البيانات
        /// Validate Data
        /// </summary>
        public bool IsValid()
        {
            return ShiftId > 0 &&
                   !string.IsNullOrWhiteSpace(Description) &&
                   Amount > 0 &&
                   EmployeeId > 0 &&
                   ExpenseDate <= DateTime.Now;
        }
    }
}
