using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using PizzaRestoApp.Models;

namespace PizzaRestoApp.Repositories
{
    /// <summary>
    /// مستودع عمولات الموظفين (البرسنتاج)
    /// Employee Commission Repository
    /// </summary>
    public class EmployeeCommissionRepository : BaseRepository<EmployeeCommission>
    {
        public EmployeeCommissionRepository() : base("employee_commissions") { }

        /// <summary>
        /// إضافة عمولة موظف جديدة
        /// Add new employee commission
        /// </summary>
        public override async Task<int> AddAsync(EmployeeCommission commission)
        {
            var sql = @"
                INSERT INTO employee_commissions (employee_id, order_id, commission_rate, 
                                                 commission_amount, commission_date, created_at)
                VALUES (@EmployeeId, @OrderId, @CommissionRate, 
                        @CommissionAmount, @CommissionDate, @CreatedAt);
                SELECT LAST_INSERT_ID();";

            commission.CreatedAt = DateTime.Now;
            
            using var connection = CreateConnection();
            return await connection.QuerySingleAsync<int>(sql, commission);
        }

        /// <summary>
        /// تحديث عمولة موظف موجودة
        /// Update existing employee commission
        /// </summary>
        public override async Task<bool> UpdateAsync(EmployeeCommission commission)
        {
            var sql = @"
                UPDATE employee_commissions 
                SET commission_rate = @CommissionRate, commission_amount = @CommissionAmount,
                    commission_date = @CommissionDate
                WHERE id = @Id";

            var affectedRows = await ExecuteAsync(sql, commission);
            return affectedRows > 0;
        }

        /// <summary>
        /// الحصول على عمولات الموظف حسب التاريخ
        /// Get employee commissions by date range
        /// </summary>
        public async Task<IEnumerable<EmployeeCommission>> GetByEmployeeAndDateRangeAsync(int employeeId, DateTime startDate, DateTime endDate)
        {
            var sql = @"
                SELECT ec.*, 
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName,
                       o.order_number as OrderNumber, o.total_amount as OrderTotalAmount
                FROM employee_commissions ec
                INNER JOIN employees e ON ec.employee_id = e.id
                INNER JOIN orders o ON ec.order_id = o.id
                WHERE ec.employee_id = @EmployeeId 
                  AND DATE(ec.commission_date) BETWEEN @StartDate AND @EndDate
                ORDER BY ec.commission_date DESC";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<EmployeeCommission, Employee, Order, EmployeeCommission>(
                sql,
                (commission, employee, order) =>
                {
                    commission.Employee = employee;
                    commission.Order = order;
                    return commission;
                },
                new { EmployeeId = employeeId, StartDate = startDate.Date, EndDate = endDate.Date },
                splitOn: "EmployeeFirstName,OrderNumber"
            );

            return result;
        }

        /// <summary>
        /// الحصول على إجمالي عمولات الموظف
        /// Get total employee commissions
        /// </summary>
        public async Task<decimal> GetTotalCommissionsByEmployeeAsync(int employeeId, DateTime? startDate = null, DateTime? endDate = null)
        {
            var sql = @"
                SELECT COALESCE(SUM(commission_amount), 0)
                FROM employee_commissions
                WHERE employee_id = @EmployeeId";

            var parameters = new { EmployeeId = employeeId };

            if (startDate.HasValue && endDate.HasValue)
            {
                sql += " AND DATE(commission_date) BETWEEN @StartDate AND @EndDate";
                parameters = new { EmployeeId = employeeId, StartDate = startDate.Value.Date, EndDate = endDate.Value.Date };
            }

            return await QueryFirstOrDefaultAsync<decimal>(sql, parameters);
        }

        /// <summary>
        /// الحصول على عمولات جميع الموظفين حسب التاريخ
        /// Get all employee commissions by date range
        /// </summary>
        public async Task<IEnumerable<EmployeeCommission>> GetAllByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            var sql = @"
                SELECT ec.*, 
                       e.first_name as EmployeeFirstName, e.last_name as EmployeeLastName,
                       o.order_number as OrderNumber, o.total_amount as OrderTotalAmount
                FROM employee_commissions ec
                INNER JOIN employees e ON ec.employee_id = e.id
                INNER JOIN orders o ON ec.order_id = o.id
                WHERE DATE(ec.commission_date) BETWEEN @StartDate AND @EndDate
                ORDER BY ec.commission_date DESC, e.first_name";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<EmployeeCommission, Employee, Order, EmployeeCommission>(
                sql,
                (commission, employee, order) =>
                {
                    commission.Employee = employee;
                    commission.Order = order;
                    return commission;
                },
                new { StartDate = startDate.Date, EndDate = endDate.Date },
                splitOn: "EmployeeFirstName,OrderNumber"
            );

            return result;
        }

        /// <summary>
        /// التحقق من وجود عمولة للطلب
        /// Check if commission exists for order
        /// </summary>
        public async Task<bool> ExistsForOrderAsync(int employeeId, int orderId)
        {
            var sql = "SELECT COUNT(1) FROM employee_commissions WHERE employee_id = @EmployeeId AND order_id = @OrderId";
            var count = await QueryFirstOrDefaultAsync<int>(sql, new { EmployeeId = employeeId, OrderId = orderId });
            return count > 0;
        }

        /// <summary>
        /// حذف عمولة الطلب
        /// Delete commission for order
        /// </summary>
        public async Task<bool> DeleteByOrderAsync(int employeeId, int orderId)
        {
            var sql = "DELETE FROM employee_commissions WHERE employee_id = @EmployeeId AND order_id = @OrderId";
            var affectedRows = await ExecuteAsync(sql, new { EmployeeId = employeeId, OrderId = orderId });
            return affectedRows > 0;
        }

        /// <summary>
        /// تقرير عمولات الموظفين
        /// Employee commissions report
        /// </summary>
        public async Task<IEnumerable<dynamic>> GetCommissionReportAsync(DateTime startDate, DateTime endDate)
        {
            var sql = @"
                SELECT 
                    e.id as EmployeeId,
                    CONCAT(e.first_name, ' ', e.last_name) as EmployeeName,
                    e.percentage_rate as CommissionRate,
                    COUNT(ec.id) as OrderCount,
                    SUM(o.total_amount) as TotalSales,
                    SUM(ec.commission_amount) as TotalCommission
                FROM employees e
                LEFT JOIN employee_commissions ec ON e.id = ec.employee_id 
                    AND DATE(ec.commission_date) BETWEEN @StartDate AND @EndDate
                LEFT JOIN orders o ON ec.order_id = o.id
                WHERE e.is_active = 1 AND e.percentage_rate > 0
                GROUP BY e.id, e.first_name, e.last_name, e.percentage_rate
                ORDER BY TotalCommission DESC";

            using var connection = CreateConnection();
            return await connection.QueryAsync(sql, new { StartDate = startDate.Date, EndDate = endDate.Date });
        }
    }
}
