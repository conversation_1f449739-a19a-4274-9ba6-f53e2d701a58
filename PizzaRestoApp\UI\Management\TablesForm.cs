using System;
using System.Linq;
using System.Windows.Forms;
using PizzaRestoApp.Models;
using PizzaRestoApp.Repositories;
using PizzaRestoApp.Utils;

namespace PizzaRestoApp.UI.Management
{
    /// <summary>
    /// شاشة إدارة الطاولات
    /// Tables Management Form
    /// </summary>
    public partial class TablesForm : Form
    {
        private readonly RestaurantTableRepository _tableRepository;

        public TablesForm()
        {
            InitializeComponent();
            _tableRepository = new RestaurantTableRepository();

            this.Text = "إدارة الطاولات - Tables Management";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            LoadData();
        }

        private async void LoadData()
        {
            try
            {
                // إعداد حالات الطاولات
                cmbStatus.Items.Add(new { Text = "متاحة", Value = TableStatus.Available });
                cmbStatus.Items.Add(new { Text = "محجوزة", Value = TableStatus.Reserved });
                cmbStatus.Items.Add(new { Text = "مشغولة", Value = TableStatus.Occupied });
                cmbStatus.Items.Add(new { Text = "خارج الخدمة", Value = TableStatus.OutOfService });
                cmbStatus.DisplayMember = "Text";
                cmbStatus.ValueMember = "Value";
                cmbStatus.SelectedIndex = 0;

                // تحميل الطاولات
                await LoadTables();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async System.Threading.Tasks.Task LoadTables()
        {
            var tables = await _tableRepository.GetAllAsync();
            dgvTables.DataSource = tables.ToList();

            // تخصيص أعمدة DataGridView
            if (dgvTables.Columns.Count > 0)
            {
                dgvTables.Columns["Id"].HeaderText = "المعرف";
                dgvTables.Columns["TableNumber"].HeaderText = "رقم الطاولة";
                dgvTables.Columns["Capacity"].HeaderText = "السعة";
                dgvTables.Columns["Status"].HeaderText = "الحالة";
                dgvTables.Columns["Location"].HeaderText = "الموقع";
                dgvTables.Columns["IsActive"].HeaderText = "نشطة";

                // إخفاء الأعمدة غير المطلوبة
                dgvTables.Columns["CreatedAt"].Visible = false;
                dgvTables.Columns["UpdatedAt"].Visible = false;
            }
        }

        private void ClearForm()
        {
            txtTableNumber.Clear();
            numCapacity.Value = 2;
            cmbStatus.SelectedIndex = 0;
            txtLocation.Clear();
            chkIsActive.Checked = true;

            btnAdd.Enabled = true;
            btnUpdate.Enabled = false;
            btnDelete.Enabled = false;
        }

        private async void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;

                var table = new RestaurantTable
                {
                    TableNumber = txtTableNumber.Text.Trim(),
                    Capacity = (int)numCapacity.Value,
                    Status = (TableStatus)((dynamic)cmbStatus.SelectedItem).Value,
                    Location = txtLocation.Text.Trim(),
                    IsActive = chkIsActive.Checked
                };

                await _tableRepository.AddAsync(table);
                MessageBox.Show("تم إضافة الطاولة بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                ClearForm();
                await LoadTables();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الطاولة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnUpdate_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateForm()) return;
                if (dgvTables.CurrentRow == null) return;

                var tableId = (int)dgvTables.CurrentRow.Cells["Id"].Value;
                var table = await _tableRepository.GetByIdAsync(tableId);

                if (table != null)
                {
                    table.TableNumber = txtTableNumber.Text.Trim();
                    table.Capacity = (int)numCapacity.Value;
                    table.Status = (TableStatus)((dynamic)cmbStatus.SelectedItem).Value;
                    table.Location = txtLocation.Text.Trim();
                    table.IsActive = chkIsActive.Checked;

                    await _tableRepository.UpdateAsync(table);
                    MessageBox.Show("تم تحديث الطاولة بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    ClearForm();
                    await LoadTables();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الطاولة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvTables.CurrentRow == null) return;

                if (MessageBox.Show("هل أنت متأكد من حذف هذه الطاولة؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                {
                    var tableId = (int)dgvTables.CurrentRow.Cells["Id"].Value;
                    await _tableRepository.DeleteAsync(tableId);

                    MessageBox.Show("تم حذف الطاولة بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    ClearForm();
                    await LoadTables();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الطاولة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dgvTables_SelectionChanged(object sender, EventArgs e)
        {
            if (dgvTables.CurrentRow != null)
            {
                var row = dgvTables.CurrentRow;

                txtTableNumber.Text = row.Cells["TableNumber"].Value?.ToString() ?? "";
                numCapacity.Value = Convert.ToDecimal(row.Cells["Capacity"].Value ?? 2);
                txtLocation.Text = row.Cells["Location"].Value?.ToString() ?? "";
                chkIsActive.Checked = Convert.ToBoolean(row.Cells["IsActive"].Value);

                var status = (TableStatus)Enum.Parse(typeof(TableStatus), row.Cells["Status"].Value.ToString());
                cmbStatus.SelectedValue = status;

                btnAdd.Enabled = false;
                btnUpdate.Enabled = true;
                btnDelete.Enabled = true;
            }
        }

        private async void btnChangeStatus_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgvTables.CurrentRow == null) return;

                var tableId = (int)dgvTables.CurrentRow.Cells["Id"].Value;
                var newStatus = (TableStatus)((dynamic)cmbStatus.SelectedItem).Value;

                await _tableRepository.UpdateStatusAsync(tableId, newStatus);

                MessageBox.Show("تم تحديث حالة الطاولة بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                await LoadTables();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث حالة الطاولة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(txtTableNumber.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الطاولة", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtTableNumber.Focus();
                return false;
            }

            if (numCapacity.Value < 1)
            {
                MessageBox.Show("يرجى إدخال سعة صحيحة للطاولة", "تحقق من البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                numCapacity.Focus();
                return false;
            }

            return true;
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private async void btnAvailableTables_Click(object sender, EventArgs e)
        {
            try
            {
                var availableTables = await _tableRepository.GetAvailableTablesAsync();

                if (availableTables.Any())
                {
                    var message = "الطاولات المتاحة:\n\n";
                    foreach (var table in availableTables)
                    {
                        message += $"• طاولة رقم {table.TableNumber} - السعة: {table.Capacity} أشخاص\n";
                    }

                    MessageBox.Show(message, "الطاولات المتاحة",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("لا توجد طاولات متاحة حالياً", "الطاولات المتاحة",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض الطاولات المتاحة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
